import os
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QCheckBox, QSpinBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFileDialog, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSlot
from xtream_code_checker import Xtream<PERSON>ode<PERSON>he<PERSON>
from config import get_config, get_telegram_bot_token

class XtreamCodeTab(QWidget):
    def __init__(self):
        super().__init__()

        # Create checker
        self.checker = XtreamCodeChecker()
        self.checker.result_signal.connect(self.on_result)
        self.checker.progress_signal.connect(self.on_progress)
        self.checker.status_signal.connect(self.on_status)

        # Initialize UI
        self.init_ui()

        # Load default values from config
        self.load_default_values()

        # Update checker settings with loaded values
        self.update_settings()

        # Connect signals
        self.connect_signals()

    def init_ui(self):
        """Initialize UI components"""
        # Main layout
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)

        # Configuration section
        config_label = QLabel("Configuration")
        config_label.setStyleSheet("font-weight: bold;")
        main_layout.addWidget(config_label)

        # URLs section
        urls_layout = QVBoxLayout()
        urls_label = QLabel("URLs:")
        urls_layout.addWidget(urls_label)

        self.urls_text = QTextEdit()
        self.urls_text.setPlaceholderText("Enter URLs in format http://example.com:8080/get.php?username=user&password=pass, one per line")
        urls_layout.addWidget(self.urls_text)

        # Load URLs button
        load_urls_layout = QHBoxLayout()
        self.load_urls_btn = QPushButton("Load URLs")
        self.clear_urls_btn = QPushButton("Clear")
        self.remove_duplicates_btn = QPushButton("Remove Duplicates")
        load_urls_layout.addWidget(self.load_urls_btn)
        load_urls_layout.addWidget(self.clear_urls_btn)
        load_urls_layout.addWidget(self.remove_duplicates_btn)
        urls_layout.addLayout(load_urls_layout)

        main_layout.addLayout(urls_layout)

        # Proxy section
        proxy_layout = QHBoxLayout()
        self.use_proxy_cb = QCheckBox("Use Proxy")
        self.load_proxy_btn = QPushButton("Load Proxy List")
        proxy_count_label = QLabel("Proxies: 0")
        self.proxy_count_label = proxy_count_label
        proxy_layout.addWidget(self.use_proxy_cb)
        proxy_layout.addWidget(self.load_proxy_btn)
        proxy_layout.addWidget(proxy_count_label)
        proxy_layout.addStretch()
        main_layout.addLayout(proxy_layout)

        # Thread and timeout settings
        settings_layout = QHBoxLayout()
        max_threads_label = QLabel("Max Threads:")
        self.max_threads_spin = QSpinBox()
        self.max_threads_spin.setRange(1, 100)
        self.max_threads_spin.setValue(10)
        timeout_label = QLabel("Timeout (sec):")
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(1, 60)
        self.timeout_spin.setValue(5)
        settings_layout.addWidget(max_threads_label)
        settings_layout.addWidget(self.max_threads_spin)
        settings_layout.addWidget(timeout_label)
        settings_layout.addWidget(self.timeout_spin)
        settings_layout.addStretch()
        main_layout.addLayout(settings_layout)

        # Auto-save option
        self.auto_save_cb = QCheckBox("Auto-save good results to file")
        self.auto_save_cb.setChecked(True)
        main_layout.addWidget(self.auto_save_cb)

        # Telegram settings
        telegram_label = QLabel("Telegram Settings")
        telegram_label.setStyleSheet("font-weight: bold;")
        main_layout.addWidget(telegram_label)

        telegram_layout = QVBoxLayout()

        # Enable Telegram
        self.enable_telegram_cb = QCheckBox("Enable Telegram Notifications")
        telegram_layout.addWidget(self.enable_telegram_cb)

        # Bot token
        bot_token_layout = QHBoxLayout()
        bot_token_label = QLabel("Bot Token:")
        self.bot_token_edit = QLineEdit()
        bot_token_layout.addWidget(bot_token_label)
        bot_token_layout.addWidget(self.bot_token_edit)
        telegram_layout.addLayout(bot_token_layout)

        # Chat ID
        chat_id_layout = QHBoxLayout()
        chat_id_label = QLabel("Chat ID:")
        self.chat_id_edit = QLineEdit()
        chat_id_layout.addWidget(chat_id_label)
        chat_id_layout.addWidget(self.chat_id_edit)
        telegram_layout.addLayout(chat_id_layout)

        # Test button
        self.test_telegram_btn = QPushButton("Test Telegram Bot")
        telegram_layout.addWidget(self.test_telegram_btn)

        main_layout.addLayout(telegram_layout)

        # Control buttons
        control_layout = QHBoxLayout()
        self.start_btn = QPushButton("Start Check")
        self.stop_btn = QPushButton("Stop Check")
        self.clear_results_btn = QPushButton("Clear Results")
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.clear_results_btn)
        main_layout.addLayout(control_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready")
        main_layout.addWidget(self.status_label)

        # Results table
        self.results_table = QTableWidget(0, 7)
        self.results_table.setHorizontalHeaderLabels(["Portal", "Username", "Password", "Status", "Expires", "Max Conn", "Categories"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        main_layout.addWidget(self.results_table)

    def load_default_values(self):
        """Load default values from config"""
        # Load telegram settings from config
        self.enable_telegram_cb.setChecked(get_config('telegram_enabled', False))
        self.bot_token_edit.setText(get_telegram_bot_token('xtream_iptv'))
        self.chat_id_edit.setText(get_config('telegram_chat_id', ""))

        # Load other settings from config
        self.max_threads_spin.setValue(get_config('max_threads', 10))
        self.timeout_spin.setValue(get_config('timeout', 10))
        self.auto_save_cb.setChecked(get_config('auto_save', True))

    def connect_signals(self):
        """Connect signals to slots"""
        self.load_urls_btn.clicked.connect(self.load_urls)
        self.clear_urls_btn.clicked.connect(self.clear_urls)
        self.remove_duplicates_btn.clicked.connect(self.remove_duplicates)
        self.load_proxy_btn.clicked.connect(self.load_proxy)
        self.start_btn.clicked.connect(self.start_check)
        self.stop_btn.clicked.connect(self.stop_check)
        self.clear_results_btn.clicked.connect(self.clear_results)
        self.test_telegram_btn.clicked.connect(self.test_telegram)

        # Update checker settings when changed
        self.max_threads_spin.valueChanged.connect(self.update_settings)
        self.timeout_spin.valueChanged.connect(self.update_settings)
        self.auto_save_cb.stateChanged.connect(self.update_settings)
        self.enable_telegram_cb.stateChanged.connect(self.update_settings)
        self.bot_token_edit.textChanged.connect(self.update_settings)
        self.chat_id_edit.textChanged.connect(self.update_settings)

    def update_settings(self):
        """Update checker settings"""
        self.checker.max_threads = self.max_threads_spin.value()
        self.checker.timeout = self.timeout_spin.value()
        self.checker.auto_save = self.auto_save_cb.isChecked()
        self.checker.telegram_enabled = self.enable_telegram_cb.isChecked()
        self.checker.telegram_bot_token = self.bot_token_edit.text()
        self.checker.telegram_chat_id = self.chat_id_edit.text()

    def load_urls(self):
        """Load URLs from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open URLs File", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    self.urls_text.setText(f.read())
            except Exception as e:
                self.status_label.setText(f"Error loading URLs: {str(e)}")

    def clear_urls(self):
        """Clear URLs"""
        self.urls_text.clear()

    def remove_duplicates(self):
        """Remove duplicate URLs from the list"""
        urls_text = self.urls_text.toPlainText().strip()
        if not urls_text:
            return

        # Get unique URLs while preserving order
        urls_list = [line.strip() for line in urls_text.splitlines() if line.strip()]
        unique_urls = []
        seen = set()
        for url in urls_list:
            if url.lower() not in seen:
                seen.add(url.lower())
                unique_urls.append(url)

        # Update the text area with unique URLs
        self.urls_text.setText("\n".join(unique_urls))

        # Show message about removed duplicates
        removed_count = len(urls_list) - len(unique_urls)
        if removed_count > 0:
            self.status_label.setText(f"Removed {removed_count} duplicate URLs")
        else:
            self.status_label.setText("No duplicate URLs found")

    def load_proxy(self):
        """Load proxy list from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Proxy List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            count = self.checker.load_proxies(file_path)
            self.proxy_count_label.setText(f"Proxies: {count}")

    def start_check(self):
        """Start checking"""
        # Get URLs
        urls_text = self.urls_text.toPlainText()
        urls = [url.strip() for url in urls_text.split('\n') if url.strip()]

        if not urls:
            self.status_label.setText("No URLs to check")
            return

        # Update settings
        self.update_settings()

        # Clear results
        self.clear_results()

        # Start checking
        use_proxy = self.use_proxy_cb.isChecked()
        if self.checker.start_check(urls, use_proxy):
            self.status_label.setText("Checking...")
            self.progress_bar.setValue(0)
        else:
            self.status_label.setText("Failed to start checking")

    def stop_check(self):
        """Stop checking"""
        self.checker.stop_check()
        self.status_label.setText("Stopped")

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)

    def test_telegram(self):
        """Test Telegram bot"""
        self.update_settings()

        if not self.checker.telegram_bot_token or not self.checker.telegram_chat_id:
            self.status_label.setText("Bot token and chat ID are required")
            return

        try:
            import requests

            message = "🧪 *Test Message*\n\nThis is a test message from IPTV Tools."

            url = f"https://api.telegram.org/bot{self.checker.telegram_bot_token}/sendMessage"
            payload = {
                "chat_id": self.checker.telegram_chat_id,
                "text": message,
                "parse_mode": "Markdown"
            }

            response = requests.post(url, json=payload, timeout=10)

            if response.status_code == 200:
                self.status_label.setText("Telegram test successful")
            else:
                self.status_label.setText(f"Telegram test failed: {response.text}")
        except Exception as e:
            self.status_label.setText(f"Telegram test failed: {str(e)}")

    @pyqtSlot(dict)
    def on_result(self, result):
        """Handle result signal"""
        # Add result to table
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        # Set data
        self.results_table.setItem(row, 0, QTableWidgetItem(result.get('portal', '')))
        self.results_table.setItem(row, 1, QTableWidgetItem(result.get('username', '')))
        self.results_table.setItem(row, 2, QTableWidgetItem(result.get('password', '')))
        self.results_table.setItem(row, 3, QTableWidgetItem(result.get('status', '')))
        self.results_table.setItem(row, 4, QTableWidgetItem(result.get('expires', '')))
        self.results_table.setItem(row, 5, QTableWidgetItem(str(result.get('max_connections', 0))))
        self.results_table.setItem(row, 6, QTableWidgetItem(str(result.get('categories', 0))))

        # Set color based on status
        status = result.get('status', '')
        for col in range(self.results_table.columnCount()):
            item = self.results_table.item(row, col)
            if item:
                if 'Active' in status and result.get('is_valid', False):
                    item.setBackground(Qt.green)
                elif 'Error' in status or 'Invalid' in status:
                    item.setBackground(Qt.red)
                elif 'Expired' in status:
                    item.setBackground(Qt.yellow)

    @pyqtSlot(int, int)
    def on_progress(self, current, total):
        """Handle progress signal"""
        if total > 0:
            percent = int(current * 100 / total)
            self.progress_bar.setValue(percent)

    @pyqtSlot(str)
    def on_status(self, status):
        """Handle status signal"""
        self.status_label.setText(status)
