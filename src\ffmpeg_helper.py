"""
FFmpeg Helper module for IPTV CHECKER - TWINS.
This module provides functionality to check for and download FFmpeg if needed.
"""

import os
import sys
import logging
import subprocess
import tempfile
import zipfile
import shutil
from urllib.request import urlretrieve
from PyQt5.QtWidgets import QMessageBox

# Get logger
logger = logging.getLogger("iptv_checker.ffmpeg_helper")

def check_ffmpeg():
    """
    Check if FFmpeg is installed and download it if not.
    
    Returns:
        str: Path to FFmpeg executable
    """
    # First check if ffmpeg is in the same directory as the application
    app_dir = os.path.dirname(os.path.abspath(__file__))
    ffmpeg_path = os.path.join(app_dir, 'ffmpeg.exe')
    
    if os.path.exists(ffmpeg_path):
        logger.info(f"FFmpeg found at: {ffmpeg_path}")
        return ffmpeg_path
    
    # Then check if ffmpeg is in PATH
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['where', 'ffmpeg'], 
                                   stdout=subprocess.PIPE, 
                                   stderr=subprocess.PIPE,
                                   text=True)
            if result.returncode == 0:
                ffmpeg_path = result.stdout.strip().split('\n')[0]
                logger.info(f"FFmpeg found in PATH: {ffmpeg_path}")
                return ffmpeg_path
        else:  # Unix-like
            result = subprocess.run(['which', 'ffmpeg'], 
                                   stdout=subprocess.PIPE, 
                                   stderr=subprocess.PIPE,
                                   text=True)
            if result.returncode == 0:
                ffmpeg_path = result.stdout.strip()
                logger.info(f"FFmpeg found in PATH: {ffmpeg_path}")
                return ffmpeg_path
    except Exception as e:
        logger.error(f"Error checking for FFmpeg in PATH: {str(e)}")
    
    # If we get here, FFmpeg is not installed
    logger.warning("FFmpeg not found, asking user to download")
    
    # Ask user if they want to download FFmpeg
    reply = QMessageBox.question(
        None, 
        "FFmpeg Not Found", 
        "FFmpeg is required for stream analysis but was not found on your system. "
        "Would you like to download and install it now?",
        QMessageBox.Yes | QMessageBox.No, 
        QMessageBox.Yes
    )
    
    if reply == QMessageBox.Yes:
        try:
            # Download and install FFmpeg
            ffmpeg_path = download_ffmpeg(app_dir)
            if ffmpeg_path:
                logger.info(f"FFmpeg downloaded and installed at: {ffmpeg_path}")
                return ffmpeg_path
        except Exception as e:
            logger.error(f"Error downloading FFmpeg: {str(e)}")
            QMessageBox.critical(
                None,
                "Error",
                f"Failed to download FFmpeg: {str(e)}\n\n"
                "Please download and install FFmpeg manually from https://ffmpeg.org/download.html"
            )
    else:
        logger.info("User declined to download FFmpeg")
        QMessageBox.information(
            None,
            "FFmpeg Required",
            "Stream analysis requires FFmpeg to work properly.\n\n"
            "Please download and install FFmpeg from https://ffmpeg.org/download.html"
        )
    
    # Return default command name as fallback
    return 'ffmpeg'

def download_ffmpeg(install_dir):
    """
    Download and install FFmpeg.
    
    Args:
        install_dir (str): Directory to install FFmpeg
        
    Returns:
        str: Path to FFmpeg executable
    """
    # FFmpeg download URL for Windows (essentials build)
    ffmpeg_url = "https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip"
    
    # Create temporary directory for download
    temp_dir = tempfile.mkdtemp()
    zip_path = os.path.join(temp_dir, "ffmpeg.zip")
    
    try:
        # Show download progress
        QMessageBox.information(
            None,
            "Downloading FFmpeg",
            "Downloading FFmpeg... This may take a few minutes."
        )
        
        # Download FFmpeg
        logger.info(f"Downloading FFmpeg from {ffmpeg_url}")
        urlretrieve(ffmpeg_url, zip_path)
        
        # Extract FFmpeg
        logger.info("Extracting FFmpeg")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        # Find the bin directory in the extracted files
        extracted_dir = None
        for item in os.listdir(temp_dir):
            if item.startswith('ffmpeg-') and os.path.isdir(os.path.join(temp_dir, item)):
                extracted_dir = os.path.join(temp_dir, item)
                break
        
        if not extracted_dir:
            raise Exception("Could not find extracted FFmpeg directory")
        
        # Find the bin directory
        bin_dir = os.path.join(extracted_dir, 'bin')
        if not os.path.exists(bin_dir):
            raise Exception("Could not find FFmpeg bin directory")
        
        # Copy FFmpeg executable to install directory
        ffmpeg_exe = os.path.join(bin_dir, 'ffmpeg.exe')
        if not os.path.exists(ffmpeg_exe):
            raise Exception("Could not find FFmpeg executable")
        
        # Copy to install directory
        dest_path = os.path.join(install_dir, 'ffmpeg.exe')
        shutil.copy2(ffmpeg_exe, dest_path)
        
        logger.info(f"FFmpeg installed to {dest_path}")
        return dest_path
    
    except Exception as e:
        logger.error(f"Error downloading FFmpeg: {str(e)}")
        raise
    
    finally:
        # Clean up temporary directory
        try:
            shutil.rmtree(temp_dir)
        except:
            pass
