#!/usr/bin/env python
"""
IPTV Tools Launcher
This script launches the IPTV Tools application.
"""

import sys
import os

# Add the current directory and src directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'src'))

# Change working directory to the script directory
os.chdir(current_dir)

# Import the main function from the app module
from src.app import main

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Error starting application: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
