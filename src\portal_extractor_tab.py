import sys
import json
import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QCheckBox, QSpinBox, QFileDialog, QProgressBar,
                            QGroupBox, QMessageBox, QRadioButton, QButtonGroup, QMenu)
from PyQt5.QtGui import QColor
from PyQt5.QtCore import Qt

from portal_extractor import PortalExtractor

class PortalExtractorTab(QWidget):
    """Tab for Stalker Portal Extraction functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.extractor = PortalExtractor()
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        layout = QVBoxLayout()

        # Configuration group
        config_group = QGroupBox("Stalker Portal Extraction Configuration")
        config_layout = QVBoxLayout()

        # Extraction method
        method_group = QGroupBox("Extraction Method")
        method_layout = QVBoxLayout()

        self.method_group = QButtonGroup(self)
        self.online_method = QRadioButton("Search Online for Latest Stalker Portal Lists")
        self.file_method = QRadioButton("Extract from File")
        self.online_method.setChecked(True)

        self.method_group.addButton(self.online_method)
        self.method_group.addButton(self.file_method)

        method_layout.addWidget(self.online_method)
        method_layout.addWidget(self.file_method)

        method_group.setLayout(method_layout)
        config_layout.addWidget(method_group)

        # File input (for file method)
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("File:"))
        self.file_input = QLineEdit()
        self.file_input.setPlaceholderText("Select a file with Stalker Portal URLs")
        self.file_input.setEnabled(False)
        file_layout.addWidget(self.file_input)

        self.browse_button = QPushButton("Browse")
        self.browse_button.clicked.connect(self.browse_file)
        self.browse_button.setEnabled(False)
        file_layout.addWidget(self.browse_button)

        config_layout.addLayout(file_layout)

        # Connect radio buttons to enable/disable file input
        self.online_method.toggled.connect(self.toggle_file_input)
        self.file_method.toggled.connect(self.toggle_file_input)

        # Thread settings
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Max Threads:"))
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(100)
        self.thread_count.setValue(10)
        thread_layout.addWidget(self.thread_count)

        thread_layout.addWidget(QLabel("Timeout (sec):"))
        self.timeout = QSpinBox()
        self.timeout.setMinimum(1)
        self.timeout.setMaximum(30)
        self.timeout.setValue(5)
        thread_layout.addWidget(self.timeout)

        thread_layout.addStretch()
        config_layout.addLayout(thread_layout)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        # Action buttons
        action_layout = QHBoxLayout()
        self.start_button = QPushButton("Start Extraction")
        self.start_button.clicked.connect(self.start_extraction)
        action_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("Stop Extraction")
        self.stop_button.clicked.connect(self.stop_extraction)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)

        self.clear_results_button = QPushButton("Clear Results")
        self.clear_results_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_results_button)

        self.export_button = QPushButton("Export Results")
        self.export_button.clicked.connect(self.export_results)
        action_layout.addWidget(self.export_button)

        layout.addLayout(action_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)

        # Results table with enhanced columns
        self.results_table = QTableWidget(0, 7)
        self.results_table.setHorizontalHeaderLabels([
            "Stalker Portal URL",
            "Type",
            "Version",
            "Country",
            "Channels",
            "Response Time (ms)",
            "Status"
        ])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.results_table)

        self.setLayout(layout)

    def connect_signals(self):
        """Connect signals from extractor to UI"""
        self.extractor.result_signal.connect(self.add_result)
        self.extractor.progress_signal.connect(self.update_progress)
        self.extractor.status_signal.connect(self.update_status)

        # Update extractor settings when UI changes
        self.thread_count.valueChanged.connect(self.update_extractor_settings)
        self.timeout.valueChanged.connect(self.update_extractor_settings)

    def update_extractor_settings(self):
        """Update extractor settings from UI"""
        self.extractor.max_threads = self.thread_count.value()
        self.extractor.timeout = self.timeout.value()

    def toggle_file_input(self):
        """Enable/disable file input based on selected method"""
        file_method_selected = self.file_method.isChecked()
        self.file_input.setEnabled(file_method_selected)
        self.browse_button.setEnabled(file_method_selected)

    def browse_file(self):
        """Browse for file with portal information"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Portal List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            self.file_input.setText(file_path)

    def start_extraction(self):
        """Start portal extraction"""
        self.results_table.setRowCount(0)
        self.update_extractor_settings()

        if self.online_method.isChecked():
            # Search online for Stalker portals
            self.status_label.setText("Searching for Stalker portals online...")
            portals = self.extractor.search_portals_online()

            if portals:
                self.status_label.setText(f"Found {len(portals)} potential Stalker portals. Starting verification...")
                self.extractor.start_verification(portals)
                self.start_button.setEnabled(False)
                self.stop_button.setEnabled(True)
                self.progress_bar.setMaximum(len(portals))
                self.progress_bar.setValue(0)
            else:
                self.status_label.setText("No Stalker portals found online.")
        else:
            # Extract from file
            file_path = self.file_input.text().strip()
            if not file_path:
                QMessageBox.warning(self, "Warning", "Please select a file.")
                return

            portals = self.extractor.extract_from_file(file_path)

            if portals:
                self.status_label.setText(f"Extracted {len(portals)} potential Stalker portals from file. Starting verification...")
                self.extractor.start_verification(portals)
                self.start_button.setEnabled(False)
                self.stop_button.setEnabled(True)
                self.progress_bar.setMaximum(len(portals))
                self.progress_bar.setValue(0)
            else:
                self.status_label.setText("No Stalker portals extracted from file.")

    def stop_extraction(self):
        """Stop portal extraction"""
        self.extractor.stop_verification()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("Extraction stopped")

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)

    def export_results(self):
        """Export results to file"""
        if self.results_table.rowCount() == 0:
            QMessageBox.warning(self, "Warning", "No results to export.")
            return

        # Create export menu
        export_menu = QMenu(self)

        # Add export options
        export_txt_action = export_menu.addAction("Export as TXT (URLs only)")
        export_m3u_action = export_menu.addAction("Export as M3U Playlist")
        export_json_action = export_menu.addAction("Export as JSON")
        export_csv_action = export_menu.addAction("Export as CSV")

        # Show menu at export button position
        action = export_menu.exec_(self.export_button.mapToGlobal(self.export_button.rect().bottomLeft()))

        if action == export_txt_action:
            self.export_as_txt()
        elif action == export_m3u_action:
            self.export_as_m3u()
        elif action == export_json_action:
            self.export_as_json()
        elif action == export_csv_action:
            self.export_as_csv()

    def export_as_txt(self):
        """Export results as plain text file with URLs only"""
        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Results as TXT", "", "Text Files (*.txt)")
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    # Write each URL to file
                    for row in range(self.results_table.rowCount()):
                        url = self.results_table.item(row, 0).text()
                        f.write(f"{url}\n")

                QMessageBox.information(self, "Success", f"Exported {self.results_table.rowCount()} portals to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    def export_as_m3u(self):
        """Export results as M3U playlist"""
        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Results as M3U", "", "M3U Files (*.m3u)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    # Write M3U header
                    f.write("#EXTM3U\n")

                    # Write each portal as a channel
                    for row in range(self.results_table.rowCount()):
                        # Only export verified portals
                        if self.results_table.item(row, 6).text() != "Verified":
                            continue

                        url = self.results_table.item(row, 0).text()
                        portal_type = self.results_table.item(row, 1).text()
                        version = self.results_table.item(row, 2).text()
                        country = self.results_table.item(row, 3).text()
                        channels = self.results_table.item(row, 4).text()
                        response_time = self.results_table.item(row, 5).text()

                        # Create channel name with portal info
                        channel_name = f"{portal_type} Portal - {country} (v{version})"

                        # Write channel info
                        f.write(f'#EXTINF:-1 tvg-id="{url}" tvg-name="{channel_name}" tvg-logo="" group-title="IPTV Portals",{channel_name}\n')
                        f.write(f'{url}\n')

                QMessageBox.information(self, "Success", f"Exported verified portals to M3U playlist: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    def export_as_json(self):
        """Export results as JSON file"""
        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Results as JSON", "", "JSON Files (*.json)")
        if file_path:
            try:
                # Create list of portal dictionaries
                portals = []

                for row in range(self.results_table.rowCount()):
                    # Only export verified portals
                    if self.results_table.item(row, 6).text() != "Verified":
                        continue

                    portal = {
                        "url": self.results_table.item(row, 0).text(),
                        "portal_type": self.results_table.item(row, 1).text(),
                        "version": self.results_table.item(row, 2).text(),
                        "country": self.results_table.item(row, 3).text(),
                        "channels_count": self.results_table.item(row, 4).text(),
                        "response_time": self.results_table.item(row, 5).text(),
                        "status": self.results_table.item(row, 6).text(),
                        "last_checked": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }

                    portals.append(portal)

                # Write to JSON file
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(portals, f, indent=4)

                QMessageBox.information(self, "Success", f"Exported {len(portals)} verified portals to JSON: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    def export_as_csv(self):
        """Export results as CSV file"""
        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Results as CSV", "", "CSV Files (*.csv)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    # Write CSV header
                    f.write("URL,Type,Version,Country,Channels,Response Time (ms),Status\n")

                    # Write each portal as a row
                    for row in range(self.results_table.rowCount()):
                        url = self.results_table.item(row, 0).text()
                        portal_type = self.results_table.item(row, 1).text()
                        version = self.results_table.item(row, 2).text()
                        country = self.results_table.item(row, 3).text()
                        channels = self.results_table.item(row, 4).text()
                        response_time = self.results_table.item(row, 5).text()
                        status = self.results_table.item(row, 6).text()

                        # Write CSV row
                        f.write(f'"{url}","{portal_type}","{version}","{country}","{channels}","{response_time}","{status}"\n')

                QMessageBox.information(self, "Success", f"Exported {self.results_table.rowCount()} portals to CSV: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    def add_result(self, result):
        """Add result to table"""
        # Skip if result is already in the table
        for row in range(self.results_table.rowCount()):
            if self.results_table.item(row, 0).text() == result.get('url', ''):
                return

        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        # Set basic data
        self.results_table.setItem(row, 0, QTableWidgetItem(result.get('url', '')))

        # Set portal type
        portal_type = result.get('portal_type', 'Unknown')
        self.results_table.setItem(row, 1, QTableWidgetItem(portal_type))

        # Set version
        version = result.get('version', 'Unknown')
        self.results_table.setItem(row, 2, QTableWidgetItem(version))

        # Set country
        country = result.get('country', 'Unknown')
        self.results_table.setItem(row, 3, QTableWidgetItem(country))

        # Set channels count
        channels_count = str(result.get('channels_count', 0))
        self.results_table.setItem(row, 4, QTableWidgetItem(channels_count))

        # Set response time
        response_time = str(result.get('response_time', 0))
        self.results_table.setItem(row, 5, QTableWidgetItem(response_time))

        # Set status
        status = "Verified" if result.get('is_portal', False) else "Potential"
        self.results_table.setItem(row, 6, QTableWidgetItem(status))

        # Highlight verified portals with color based on response time
        if result.get('is_portal', False):
            # Determine color based on response time
            response_time = result.get('response_time', 0)

            if response_time < 200:  # Fast response (< 200ms)
                bg_color = QColor(150, 255, 150)  # Bright green
            elif response_time < 500:  # Medium response (200-500ms)
                bg_color = QColor(200, 255, 200)  # Light green
            elif response_time < 1000:  # Slow response (500-1000ms)
                bg_color = QColor(255, 255, 150)  # Light yellow
            else:  # Very slow response (> 1000ms)
                bg_color = QColor(255, 200, 200)  # Light red

            for col in range(self.results_table.columnCount()):
                item = self.results_table.item(row, col)
                if item:
                    item.setBackground(bg_color)

    def update_progress(self, current, total):
        """Update progress bar"""
        self.progress_bar.setValue(current)
        if current >= total:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)

            # Count verified portals
            verified_count = 0
            for row in range(self.results_table.rowCount()):
                if self.results_table.item(row, 6).text() == "Verified":  # Status is now in column 6
                    verified_count += 1

            # Calculate statistics
            portal_types = {}
            countries = {}
            total_channels = 0
            avg_response_time = 0
            response_times = []
            fast_portals = 0
            medium_portals = 0
            slow_portals = 0

            for row in range(self.results_table.rowCount()):
                if self.results_table.item(row, 6).text() == "Verified":
                    # Count portal types
                    portal_type = self.results_table.item(row, 1).text()
                    portal_types[portal_type] = portal_types.get(portal_type, 0) + 1

                    # Count countries
                    country = self.results_table.item(row, 3).text()
                    countries[country] = countries.get(country, 0) + 1

                    # Sum channels
                    try:
                        channels = int(self.results_table.item(row, 4).text())
                        total_channels += channels
                    except:
                        pass

                    # Sum response times and categorize
                    try:
                        response_time = int(self.results_table.item(row, 5).text())
                        response_times.append(response_time)

                        # Categorize by speed
                        if response_time < 200:
                            fast_portals += 1
                        elif response_time < 500:
                            medium_portals += 1
                        else:
                            slow_portals += 1
                    except:
                        pass

            # Calculate average response time
            if response_times:
                avg_response_time = sum(response_times) / len(response_times)

            # Get top portal type
            top_portal_type = max(portal_types.items(), key=lambda x: x[1])[0] if portal_types else "Unknown"

            # Get top country
            top_country = max(countries.items(), key=lambda x: x[1])[0] if countries else "Unknown"

            # Get top 3 countries
            top_countries = sorted(countries.items(), key=lambda x: x[1], reverse=True)[:3]
            top_countries_str = ", ".join([f"{c[0]} ({c[1]})" for c in top_countries]) if top_countries else "Unknown"

            # Get top 3 portal types
            top_types = sorted(portal_types.items(), key=lambda x: x[1], reverse=True)[:3]
            top_types_str = ", ".join([f"{t[0]} ({t[1]})" for t in top_types]) if top_types else "Unknown"

            # Update status with detailed information
            self.status_label.setText(
                f"Extraction completed. Found {verified_count} verified Stalker portals out of {self.results_table.rowCount()} potential portals. "
                f"Top types: {top_types_str}. Top countries: {top_countries_str}. "
                f"Total channels: {total_channels}, Avg response: {avg_response_time:.1f}ms. "
                f"Speed: {fast_portals} fast, {medium_portals} medium, {slow_portals} slow."
            )

    def update_status(self, status):
        """Update status label"""
        self.status_label.setText(status)
