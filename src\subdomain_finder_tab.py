"""
Subdomain Finder Tab for IPTV Tools.
This module provides a GUI for the Subdomain Finder functionality.
"""

import os
import json
import csv
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QCheckBox, QSpinBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFileDialog, QProgressBar,
                            QComboBox, QMenu, QAction, QMessageBox, QToolButton, QFrame,
                            QGroupBox, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, pyqtSlot, QSize
from PyQt5.QtGui import QColor, QIcon, QFont
from subdomain_finder import SubdomainFinder

class SubdomainFinderTab(QWidget):
    """Tab for Subdomain Finder functionality"""

    def __init__(self):
        super().__init__()

        # Create finder
        self.finder = SubdomainFinder()
        self.finder.result_signal.connect(self.on_result)
        self.finder.progress_signal.connect(self.on_progress)
        self.finder.status_signal.connect(self.on_status)
        self.finder.finished_signal.connect(self.on_finished)

        # Initialize UI
        self.init_ui()

        # Connect signals
        self.connect_signals()

    def init_ui(self):
        """Initialize UI components"""
        # Main layout
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)

        # Set modern style with improved colors
        self.setStyleSheet("""
            QWidget {
                background-color: #2d3250;
                color: white;
                font-size: 10pt;
            }
            QLabel {
                font-size: 10pt;
                color: white;
            }
            QPushButton {
                background-color: #4a5de8;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover { background-color: #5a6df8; }
            QPushButton:pressed { background-color: #3a4dd8; }
            QCheckBox {
                font-size: 10pt;
                color: white;
            }
            QSpinBox {
                padding: 5px;
                background-color: #3d4270;
                color: white;
                border: 1px solid #5a6df8;
                border-radius: 3px;
            }
            QLineEdit {
                padding: 8px;
                background-color: #3d4270;
                color: white;
                border: 1px solid #5a6df8;
                border-radius: 3px;
                font-size: 11pt;
            }
            QTextEdit {
                padding: 5px;
                background-color: #3d4270;
                color: white;
                border: 1px solid #5a6df8;
            }
            QTableWidget {
                alternate-background-color: #3d4270;
                gridline-color: #5a6df8;
                background-color: #2d3250;
                color: white;
                border: 1px solid #5a6df8;
            }
            QHeaderView::section {
                background-color: #4a5de8;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
            QProgressBar {
                border: 1px solid #5a6df8;
                border-radius: 5px;
                text-align: center;
                background-color: #3d4270;
                color: white;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4a5de8;
                width: 10px;
                margin: 0.5px;
            }
            QListWidget {
                border: 1px solid #5a6df8;
                border-radius: 3px;
                padding: 5px;
                background-color: #3d4270;
                color: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #2d3250;
            }
            QListWidget::item:selected {
                background-color: #4a5de8;
                color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #5a6df8;
                border-radius: 5px;
                margin-top: 15px;
                padding-top: 20px;
                color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                color: white;
            }
            QFrame {
                background-color: #2d3250;
                border: 1px solid #5a6df8;
                border-radius: 5px;
            }
        """)

        # Input section
        input_frame = QFrame()
        input_frame.setFrameShape(QFrame.StyledPanel)
        input_layout = QVBoxLayout(input_frame)

        input_label = QLabel("Subdomain Finder")
        input_label.setStyleSheet("font-weight: bold; font-size: 12pt; color: #5a6df8;")
        input_layout.addWidget(input_label)

        # Domain input
        domain_layout = QHBoxLayout()
        domain_label = QLabel("Domain:")
        domain_label.setStyleSheet("font-weight: bold;")
        self.domain_edit = QLineEdit()
        self.domain_edit.setPlaceholderText("Enter domain (e.g., example.com)")
        domain_layout.addWidget(domain_label)
        domain_layout.addWidget(self.domain_edit)
        input_layout.addLayout(domain_layout)

        # Methods selection
        methods_group = QGroupBox("Discovery Methods")
        methods_layout = QHBoxLayout(methods_group)

        self.brute_cb = QCheckBox("Brute Force")
        self.brute_cb.setChecked(True)
        self.crtsh_cb = QCheckBox("Certificate Transparency (crt.sh)")
        self.crtsh_cb.setChecked(True)

        methods_layout.addWidget(self.brute_cb)
        methods_layout.addWidget(self.crtsh_cb)
        methods_layout.addStretch()

        input_layout.addWidget(methods_group)

        # Advanced options
        advanced_group = QGroupBox("Advanced Options")
        advanced_layout = QVBoxLayout(advanced_group)

        # Wordlist options
        wordlist_layout = QHBoxLayout()
        self.use_custom_wordlist_cb = QCheckBox("Use Custom Wordlist")
        self.load_wordlist_btn = QPushButton("Load Wordlist")
        self.load_wordlist_btn.setEnabled(False)
        self.wordlist_count_label = QLabel("Default wordlist: 50 words")

        wordlist_layout.addWidget(self.use_custom_wordlist_cb)
        wordlist_layout.addWidget(self.load_wordlist_btn)
        wordlist_layout.addWidget(self.wordlist_count_label)
        wordlist_layout.addStretch()

        advanced_layout.addLayout(wordlist_layout)

        # Thread and timeout settings
        settings_layout = QHBoxLayout()
        threads_label = QLabel("Threads:")
        self.threads_spin = QSpinBox()
        self.threads_spin.setRange(1, 100)
        self.threads_spin.setValue(20)

        timeout_label = QLabel("Timeout (sec):")
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(1, 60)
        self.timeout_spin.setValue(5)

        settings_layout.addWidget(threads_label)
        settings_layout.addWidget(self.threads_spin)
        settings_layout.addWidget(timeout_label)
        settings_layout.addWidget(self.timeout_spin)
        settings_layout.addStretch()

        advanced_layout.addLayout(settings_layout)

        input_layout.addWidget(advanced_group)

        # Control buttons
        control_layout = QHBoxLayout()
        self.start_btn = QPushButton("Start Scan")
        self.start_btn.setIcon(QIcon.fromTheme("media-playback-start"))
        self.start_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; font-size: 11pt; padding: 8px 15px; border-radius: 4px;")

        self.stop_btn = QPushButton("Stop Scan")
        self.stop_btn.setIcon(QIcon.fromTheme("media-playback-stop"))
        self.stop_btn.setStyleSheet("background-color: #F44336; color: white; font-weight: bold; font-size: 11pt; padding: 8px 15px; border-radius: 4px;")
        self.stop_btn.setEnabled(False)

        self.clear_btn = QPushButton("Clear Results")
        self.clear_btn.setIcon(QIcon.fromTheme("edit-clear"))

        self.export_btn = QPushButton("Export Results")
        self.export_btn.setIcon(QIcon.fromTheme("document-save"))

        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.clear_btn)
        control_layout.addWidget(self.export_btn)

        input_layout.addLayout(control_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        input_layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("font-weight: bold;")
        input_layout.addWidget(self.status_label)

        main_layout.addWidget(input_frame)

        # Results section
        results_frame = QFrame()
        results_frame.setFrameShape(QFrame.StyledPanel)
        results_layout = QVBoxLayout(results_frame)

        results_header_layout = QHBoxLayout()

        results_label = QLabel("Discovered Subdomains")
        results_label.setStyleSheet("font-weight: bold; font-size: 12pt; color: #5a6df8;")
        results_header_layout.addWidget(results_label)

        self.results_count_label = QLabel("0 subdomains found")
        results_header_layout.addWidget(self.results_count_label)
        results_header_layout.addStretch()

        results_layout.addLayout(results_header_layout)

        # Results list
        self.results_list = QListWidget()
        self.results_list.setAlternatingRowColors(True)
        results_layout.addWidget(self.results_list)

        main_layout.addWidget(results_frame)

    def connect_signals(self):
        """Connect signals to slots"""
        # Connect buttons
        self.start_btn.clicked.connect(self.start_scan)
        self.stop_btn.clicked.connect(self.stop_scan)
        self.clear_btn.clicked.connect(self.clear_results)
        self.export_btn.clicked.connect(self.show_export_menu)
        self.load_wordlist_btn.clicked.connect(self.load_wordlist)

        # Connect checkboxes
        self.use_custom_wordlist_cb.stateChanged.connect(self.toggle_wordlist_button)

    def toggle_wordlist_button(self):
        """Enable/disable wordlist button based on checkbox state"""
        self.load_wordlist_btn.setEnabled(self.use_custom_wordlist_cb.isChecked())

    def load_wordlist(self):
        """Load custom wordlist from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Wordlist File", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            if self.finder.load_wordlist(file_path):
                self.wordlist_count_label.setText(f"Custom wordlist: {len(self.finder.wordlist)} words")
            else:
                self.wordlist_count_label.setText("Failed to load wordlist")

    def start_scan(self):
        """Start subdomain scan"""
        # Get domain
        domain = self.domain_edit.text().strip()
        if not domain:
            QMessageBox.warning(self, "Warning", "Please enter a domain to scan.")
            return

        # Get methods
        methods = []
        if self.brute_cb.isChecked():
            methods.append('brute')
        if self.crtsh_cb.isChecked():
            methods.append('crtsh')

        if not methods:
            QMessageBox.warning(self, "Warning", "Please select at least one discovery method.")
            return

        # Get settings
        threads = self.threads_spin.value()
        timeout = self.timeout_spin.value()

        # Clear previous results
        self.clear_results()

        # Start scan
        if self.finder.start_scan(domain, methods, None, threads, timeout):
            self.status_label.setText(f"Scanning subdomains for {domain}...")
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress_bar.setValue(0)
        else:
            self.status_label.setText("Failed to start scan")

    def stop_scan(self):
        """Stop subdomain scan"""
        if self.finder.stop_scan():
            self.status_label.setText("Stopping scan...")
        else:
            self.status_label.setText("No scan running")

    def clear_results(self):
        """Clear results list"""
        self.results_list.clear()
        self.results_count_label.setText("0 subdomains found")

    def show_export_menu(self):
        """Show export menu with different export options"""
        if self.results_list.count() == 0:
            QMessageBox.warning(self, "Warning", "No results to export.")
            return

        # Create export menu
        export_menu = QMenu(self)

        # Add export options
        export_txt_action = export_menu.addAction("Export as TXT")
        export_csv_action = export_menu.addAction("Export as CSV")
        export_json_action = export_menu.addAction("Export as JSON")

        # Show menu at export button position
        action = export_menu.exec_(self.export_btn.mapToGlobal(self.export_btn.rect().bottomLeft()))

        if action == export_txt_action:
            self.export_as_txt()
        elif action == export_csv_action:
            self.export_as_csv()
        elif action == export_json_action:
            self.export_as_json()

    def export_as_txt(self):
        """Export results as plain text file"""
        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Results as TXT", "", "Text Files (*.txt)")
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    # Write each subdomain to file
                    for i in range(self.results_list.count()):
                        subdomain = self.results_list.item(i).text()
                        f.write(f"{subdomain}\n")

                QMessageBox.information(self, "Success", f"Exported {self.results_list.count()} subdomains to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    def export_as_csv(self):
        """Export results as CSV file"""
        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Results as CSV", "", "CSV Files (*.csv)")
        if file_path:
            try:
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)

                    # Write header
                    writer.writerow(["Subdomain"])

                    # Write data
                    for i in range(self.results_list.count()):
                        subdomain = self.results_list.item(i).text()
                        writer.writerow([subdomain])

                QMessageBox.information(self, "Success", f"Exported {self.results_list.count()} subdomains to CSV: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    def export_as_json(self):
        """Export results as JSON file"""
        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Results as JSON", "", "JSON Files (*.json)")
        if file_path:
            try:
                # Create list of subdomains
                subdomains = []
                for i in range(self.results_list.count()):
                    subdomains.append(self.results_list.item(i).text())

                # Write to JSON file
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump({"subdomains": subdomains}, f, indent=4)

                QMessageBox.information(self, "Success", f"Exported {len(subdomains)} subdomains to JSON: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    @pyqtSlot(str)
    def on_result(self, subdomain):
        """Handle result signal"""
        # Add subdomain to list
        item = QListWidgetItem(subdomain)
        item.setFont(QFont("Monospace"))
        self.results_list.addItem(item)

        # Update count
        self.results_count_label.setText(f"{self.results_list.count()} subdomains found")

    @pyqtSlot(int, int)
    def on_progress(self, current, total):
        """Handle progress signal"""
        if total > 0:
            percent = int(current * 100 / total)
            self.progress_bar.setValue(percent)

    @pyqtSlot(str)
    def on_status(self, status):
        """Handle status signal"""
        self.status_label.setText(status)

    @pyqtSlot(list)
    def on_finished(self, subdomains):
        """Handle finished signal"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setValue(100)
