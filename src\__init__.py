"""
IPTV Checker package.
This package contains modules for checking IPTV services.
"""

import logging
import os

# Configure logging
if not os.path.exists('logs'):
    os.makedirs('logs')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'checker.log')),
        logging.StreamHandler()
    ]
)

# Create logger
logger = logging.getLogger("iptv_checker")
logger.info("IPTV Checker package initialized")

# Version
__version__ = '1.0.0'
