import sys
import os
from PyQt5.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QLineEdit, QPushButton, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
                            QCheckBox, QSpinBox, QFileDialog, QProgressBar, QGroupBox,
                            QRadioButton, QMessageBox, QMenu, QAction)
from PyQt5.QtGui import QIcon, QColor, QFont
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from styles import get_status_color, get_text_color_for_background

from mac_scanner import MACScanner
from user_pass_checker import UserPass<PERSON>he<PERSON>
from m3u_checker import M3UChecker
from xtream_code_checker import <PERSON>tream<PERSON>ode<PERSON><PERSON><PERSON>
from xui_panel_checker import XUIPanel<PERSON>he<PERSON>
from portal_extractor import PortalExtractor
from subdomain_finder import Subdomain<PERSON>inder
from subdomain_finder_tab import SubdomainFinderTab
from xui_panel_tab import XUIPanelTab
from portal_extractor_tab import PortalExtractorTab
from tools_tab import ToolsTab
from config import get_config, get_telegram_bot_token

class TelegramSettingsWidget(QWidget):
    """Widget for Telegram settings"""

    def __init__(self, module_name='user_pass', parent=None):
        super().__init__(parent)
        self.module_name = module_name
        self.init_ui()
        self.load_default_values()

    def init_ui(self):
        layout = QVBoxLayout()

        # Telegram group
        telegram_group = QGroupBox("Telegram Settings")
        telegram_layout = QVBoxLayout()

        # Enable Telegram
        self.telegram_enabled = QCheckBox("Enable Telegram Notifications")
        telegram_layout.addWidget(self.telegram_enabled)

        # Bot token
        token_layout = QHBoxLayout()
        token_layout.addWidget(QLabel("Bot Token:"))
        self.bot_token = QLineEdit()
        token_layout.addWidget(self.bot_token)
        telegram_layout.addLayout(token_layout)

        # Chat ID
        chat_id_layout = QHBoxLayout()
        chat_id_layout.addWidget(QLabel("Chat ID:"))
        self.chat_id = QLineEdit()
        chat_id_layout.addWidget(self.chat_id)
        telegram_layout.addLayout(chat_id_layout)

        # Test button
        self.test_button = QPushButton("Test Telegram Bot")
        self.test_button.clicked.connect(self.test_telegram)
        telegram_layout.addWidget(self.test_button)

        telegram_group.setLayout(telegram_layout)
        layout.addWidget(telegram_group)

        # Add stretch to push everything to the top
        layout.addStretch()

        self.setLayout(layout)

    def load_default_values(self):
        """Load default values from config"""
        try:
            self.telegram_enabled.setChecked(get_config('telegram_enabled', False))

            # Set bot token directly from our predefined values
            bot_token = get_telegram_bot_token(self.module_name)
            if not bot_token:  # Fallback if config function fails
                if self.module_name == 'user_pass':
                    bot_token = "5281565638:AAFm_1DOQGZw7b2MJh7VUSsOQxopnTr--vc"
                else:  # mac_scanner
                    bot_token = "5281565638:AAFm_1DOQGZw7b2MJh7VUSsOQxopnTr--vc"
            self.bot_token.setText(bot_token)

            # Set chat ID directly
            chat_id = get_config('telegram_chat_id', "")
            if not chat_id:  # Fallback if config function fails
                chat_id = "242110769"
            self.chat_id.setText(chat_id)

            print(f"[{self.module_name.upper()}] Loaded bot token: {bot_token}")
            print(f"[{self.module_name.upper()}] Loaded chat ID: {chat_id}")
        except Exception as e:
            print(f"[{self.module_name.upper()}] Error loading default values: {e}")
            # Set hardcoded values as fallback
            self.bot_token.setText("5281565638:AAFm_1DOQGZw7b2MJh7VUSsOQxopnTr--vc")
            self.chat_id.setText("242110769")

    def test_telegram(self):
        """Test Telegram bot connection"""
        import requests

        if not self.telegram_enabled.isChecked():
            QMessageBox.warning(self, "Warning", "Telegram notifications are not enabled.")
            return

        token = self.bot_token.text().strip()
        chat_id = self.chat_id.text().strip()

        if not token or not chat_id:
            QMessageBox.warning(self, "Warning", "Please enter both Bot Token and Chat ID.")
            return

        try:
            message = "🧪 Test message from IPTV Tools by Manzera Ayenna"
            url = f"https://api.telegram.org/bot{token}/sendMessage"
            payload = {
                "chat_id": chat_id,
                "text": message
            }

            response = requests.post(url, json=payload, timeout=10)

            if response.status_code == 200:
                QMessageBox.information(self, "Success", "Test message sent successfully!")
            else:
                data = response.json()
                QMessageBox.critical(self, "Error", f"Failed to send message: {data.get('description', 'Unknown error')}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to send message: {str(e)}")

class MACTab(QWidget):
    """Tab for MAC Scanner functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.scanner = MACScanner()
        self.init_ui()
        self.load_default_values()
        self.update_scanner_settings()  # Update scanner with loaded values
        self.connect_signals()

    def init_ui(self):
        layout = QVBoxLayout()

        # Configuration group
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout()

        # Portal input
        portal_layout = QHBoxLayout()
        portal_layout.addWidget(QLabel("Portal:"))
        self.portal_input = QLineEdit()
        self.portal_input.setPlaceholderText("http://example.com:8080")
        portal_layout.addWidget(self.portal_input)
        config_layout.addLayout(portal_layout)

        # MAC input
        mac_layout = QHBoxLayout()
        mac_layout.addWidget(QLabel("MAC:"))
        self.mac_input = QTextEdit()
        self.mac_input.setPlaceholderText("Enter MAC addresses, one per line")
        self.mac_input.setMaximumHeight(100)
        mac_layout.addWidget(self.mac_input)
        config_layout.addLayout(mac_layout)

        # Buttons for loading MACs
        buttons_layout = QHBoxLayout()
        self.load_mac_button = QPushButton("Load MAC List")
        self.load_mac_button.clicked.connect(self.load_mac_list)
        buttons_layout.addWidget(self.load_mac_button)

        self.clear_mac_button = QPushButton("Clear")
        self.clear_mac_button.clicked.connect(self.clear_mac_list)
        buttons_layout.addWidget(self.clear_mac_button)

        self.remove_duplicates_button = QPushButton("Remove Duplicates")
        self.remove_duplicates_button.clicked.connect(self.remove_duplicates)
        buttons_layout.addWidget(self.remove_duplicates_button)
        config_layout.addLayout(buttons_layout)

        # Proxy settings
        proxy_layout = QHBoxLayout()
        self.use_proxy = QCheckBox("Use Proxy")
        proxy_layout.addWidget(self.use_proxy)

        self.load_proxy_button = QPushButton("Load Proxy List")
        self.load_proxy_button.clicked.connect(self.load_proxy_list)
        proxy_layout.addWidget(self.load_proxy_button)

        self.proxy_count_label = QLabel("Proxies: 0")
        proxy_layout.addWidget(self.proxy_count_label)

        proxy_layout.addStretch()
        config_layout.addLayout(proxy_layout)

        # Thread settings
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Max Threads:"))
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(100)
        self.thread_count.setValue(10)
        thread_layout.addWidget(self.thread_count)

        thread_layout.addWidget(QLabel("Timeout (sec):"))
        self.timeout = QSpinBox()
        self.timeout.setMinimum(1)
        self.timeout.setMaximum(30)
        self.timeout.setValue(5)
        thread_layout.addWidget(self.timeout)

        thread_layout.addStretch()
        config_layout.addLayout(thread_layout)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        # MAC Generator group
        mac_gen_group = QGroupBox("MAC Address Generator")
        mac_gen_layout = QVBoxLayout()

        # Prefix input
        prefix_layout = QHBoxLayout()
        prefix_layout.addWidget(QLabel("Fixed Prefix:"))
        self.prefix_input = QLineEdit()
        self.prefix_input.setPlaceholderText("e.g., 00:1A:79:")
        self.prefix_input.setText("00:1A:79:")
        prefix_layout.addWidget(self.prefix_input)
        mac_gen_layout.addLayout(prefix_layout)

        # Count input
        count_layout = QHBoxLayout()
        count_layout.addWidget(QLabel("Number of MACs:"))
        self.mac_count = QSpinBox()
        self.mac_count.setMinimum(1)
        self.mac_count.setMaximum(100000)
        self.mac_count.setValue(10000)
        count_layout.addWidget(self.mac_count)

        # Generate button
        self.generate_mac_button = QPushButton("Generate MAC Addresses")
        self.generate_mac_button.clicked.connect(self.generate_mac_addresses)
        count_layout.addWidget(self.generate_mac_button)

        mac_gen_layout.addLayout(count_layout)
        mac_gen_group.setLayout(mac_gen_layout)
        layout.addWidget(mac_gen_group)

        # Telegram settings
        self.telegram_settings = TelegramSettingsWidget('mac_scanner')
        layout.addWidget(self.telegram_settings)

        # Action buttons
        action_layout = QHBoxLayout()
        self.start_button = QPushButton("Start Scan")
        self.start_button.clicked.connect(self.start_scan)
        action_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("Stop Scan")
        self.stop_button.clicked.connect(self.stop_scan)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)

        self.clear_results_button = QPushButton("Clear Results")
        self.clear_results_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_results_button)

        layout.addLayout(action_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)

        # Results table
        self.results_table = QTableWidget(0, 5)
        self.results_table.setHorizontalHeaderLabels(["MAC", "Portal", "Status", "Created", "Expires"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.results_table)

        self.setLayout(layout)

    def load_default_values(self):
        """Load default values from config"""
        # Load settings from config
        self.thread_count.setValue(get_config('max_threads', 10))
        self.timeout.setValue(get_config('timeout', 5))

    def connect_signals(self):
        """Connect signals from scanner to UI"""
        self.scanner.result_signal.connect(self.add_result)
        self.scanner.progress_signal.connect(self.update_progress)
        self.scanner.status_signal.connect(self.update_status)

        # Update scanner settings when UI changes
        self.thread_count.valueChanged.connect(self.update_scanner_settings)
        self.timeout.valueChanged.connect(self.update_scanner_settings)
        self.telegram_settings.telegram_enabled.toggled.connect(self.update_scanner_settings)
        self.telegram_settings.bot_token.textChanged.connect(self.update_scanner_settings)
        self.telegram_settings.chat_id.textChanged.connect(self.update_scanner_settings)

    def update_scanner_settings(self):
        """Update scanner settings from UI"""
        self.scanner.max_threads = self.thread_count.value()
        self.scanner.timeout = self.timeout.value()
        self.scanner.telegram_enabled = self.telegram_settings.telegram_enabled.isChecked()
        self.scanner.telegram_bot_token = self.telegram_settings.bot_token.text().strip()
        self.scanner.telegram_chat_id = self.telegram_settings.chat_id.text().strip()

    def load_mac_list(self):
        """Load MAC list from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open MAC List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    macs = f.read()
                    self.mac_input.setText(macs)
                    self.status_label.setText(f"Loaded {len(macs.splitlines())} MACs from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load MAC list: {str(e)}")

    def clear_mac_list(self):
        """Clear MAC list"""
        self.mac_input.clear()

    def remove_duplicates(self):
        """Remove duplicate MAC addresses from the list"""
        mac_text = self.mac_input.toPlainText().strip()
        if not mac_text:
            return

        # Get unique MAC addresses while preserving order
        mac_list = [line.strip() for line in mac_text.splitlines() if line.strip()]
        unique_macs = []
        seen = set()
        for mac in mac_list:
            if mac.lower() not in seen:
                seen.add(mac.lower())
                unique_macs.append(mac)

        # Update the text area with unique MACs
        self.mac_input.setText("\n".join(unique_macs))

        # Show message about removed duplicates
        removed_count = len(mac_list) - len(unique_macs)
        if removed_count > 0:
            self.status_label.setText(f"Removed {removed_count} duplicate MAC addresses")
        else:
            self.status_label.setText("No duplicate MAC addresses found")

    def load_proxy_list(self):
        """Load proxy list from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Proxy List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                count = self.scanner.load_proxies(file_path)
                self.proxy_count_label.setText(f"Proxies: {count}")
                self.status_label.setText(f"Loaded {count} proxies from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load proxy list: {str(e)}")

    def start_scan(self):
        """Start MAC scanning"""
        portal = self.portal_input.text().strip()
        if not portal:
            QMessageBox.warning(self, "Warning", "Please enter a portal URL.")
            return

        # Add http:// if missing
        if not portal.startswith('http://') and not portal.startswith('https://'):
            portal = f"http://{portal}"
            self.portal_input.setText(portal)

        # Get MAC list
        mac_text = self.mac_input.toPlainText().strip()
        if not mac_text:
            QMessageBox.warning(self, "Warning", "Please enter at least one MAC address.")
            return

        mac_list = [line.strip() for line in mac_text.splitlines() if line.strip()]

        # Update scanner settings
        self.update_scanner_settings()

        # Test portal first
        portal_test = self.scanner.test_portal(portal)
        if not portal_test["is_accessible"]:
            warning_msg = "Warning: The portal may not be accessible.\n\n"
            if "error" in portal_test:
                warning_msg += f"Error: {portal_test['error']}\n\n"
            else:
                warning_msg += f"Base URL status: {portal_test['base_status']}\n"
                warning_msg += f"Handshake status: {portal_test['handshake_status']}\n\n"
            warning_msg += "This may result in HTTP errors during scanning.\n\nDo you want to continue anyway?"

            reply = QMessageBox.question(self, "Portal Accessibility Warning",
                                        warning_msg,
                                        QMessageBox.Yes | QMessageBox.No,
                                        QMessageBox.No)

            if reply == QMessageBox.No:
                return

        # Start scanning
        success = self.scanner.start_scan(mac_list, portal, self.use_proxy.isChecked())

        if success:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("Scanning...")
            self.progress_bar.setMaximum(len(mac_list))
            self.progress_bar.setValue(0)
        else:
            QMessageBox.critical(self, "Error", "Failed to start scanning.")

    def stop_scan(self):
        """Stop MAC scanning"""
        self.scanner.stop_scan()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("Scan stopped")

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)

    def add_result(self, result):
        """Add result to table"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(result.get('mac', '')))
        self.results_table.setItem(row, 1, QTableWidgetItem(result.get('portal', '')))
        self.results_table.setItem(row, 2, QTableWidgetItem(result.get('status', '')))
        self.results_table.setItem(row, 3, QTableWidgetItem(result.get('created', '')))
        self.results_table.setItem(row, 4, QTableWidgetItem(result.get('expires', '')))

        # Highlight row based on status
        status = result.get('status', '')
        background_color = get_status_color(status)
        text_color = get_text_color_for_background(background_color)

        for col in range(self.results_table.columnCount()):
            item = self.results_table.item(row, col)
            if item:
                item.setBackground(background_color)
                item.setForeground(text_color)

    def update_progress(self, current, total):
        """Update progress bar"""
        self.progress_bar.setValue(current)
        if current >= total:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)

            # Count active MACs vs errors
            active_count = 0
            error_count = 0
            for row in range(self.results_table.rowCount()):
                status_item = self.results_table.item(row, 2)
                if status_item:
                    status_text = status_item.text()
                    if "Active" in status_text:
                        active_count += 1
                    elif "HTTP Error" in status_text or "Error" in status_text:
                        error_count += 1

            # Show detailed completion message
            if active_count > 0:
                self.status_label.setText(f"Scan completed. Found {active_count} active MACs and {error_count} errors.")
            else:
                self.status_label.setText(f"Scan completed. No active MACs found. {error_count} errors encountered.")

    def update_status(self, status):
        """Update status label"""
        self.status_label.setText(status)

    def generate_mac_addresses(self):
        """Generate MAC addresses with fixed prefix and random suffix"""
        import random

        prefix = self.prefix_input.text().strip()
        count = self.mac_count.value()

        # Validate prefix format
        if not prefix:
            QMessageBox.warning(self, "Warning", "Please enter a MAC address prefix.")
            return

        # Remove trailing colon if present
        if prefix.endswith(':'):
            prefix = prefix[:-1]

        # Count the number of octets in the prefix
        octets = prefix.split(':')
        if len(octets) > 6:
            QMessageBox.warning(self, "Warning", "MAC address prefix is too long. It should be at most 5 octets.")
            return

        # Generate MAC addresses
        generated_macs = []
        remaining_octets = 6 - len(octets)

        for _ in range(count):
            # Generate random octets for the remaining part
            random_octets = [f"{random.randint(0, 255):02X}" for _ in range(remaining_octets)]

            # Combine prefix and random octets
            mac = f"{prefix}:{':'.join(random_octets)}" if prefix else ":".join(random_octets)

            # Ensure proper format with colons
            if ":" not in mac:
                mac_chars = list(mac)
                for i in range(2, len(mac_chars), 3):
                    if i < len(mac_chars):
                        mac_chars.insert(i, ":")
                mac = "".join(mac_chars)

            generated_macs.append(mac)

        # Add to existing MACs or replace
        current_text = self.mac_input.toPlainText().strip()
        if current_text:
            self.mac_input.setText(current_text + "\n" + "\n".join(generated_macs))
        else:
            self.mac_input.setText("\n".join(generated_macs))

        self.status_label.setText(f"Generated {count} MAC addresses with prefix {prefix}")

        # Offer to save to file if many MACs were generated
        if count > 1000:
            reply = QMessageBox.question(
                self,
                "Save to File",
                f"Do you want to save the {count} generated MAC addresses to a file?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                file_path, _ = QFileDialog.getSaveFileName(
                    self,
                    "Save MAC Addresses",
                    f"generated_macs_{prefix.replace(':', '')}.txt",
                    "Text Files (*.txt);;All Files (*)"
                )
                if file_path:
                    try:
                        with open(file_path, 'w') as f:
                            f.write("\n".join(generated_macs))
                        self.status_label.setText(f"Generated {count} MAC addresses and saved to {file_path}")
                    except Exception as e:
                        QMessageBox.critical(self, "Error", f"Failed to save MAC addresses: {str(e)}")

class UserPassTab(QWidget):
    """Tab for User-Pass Checker functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.checker = UserPassChecker()
        self.init_ui()
        self.load_default_values()
        self.update_checker_settings()  # Update checker with loaded values
        self.connect_signals()

    def init_ui(self):
        layout = QVBoxLayout()

        # Configuration group
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout()

        # Portal input
        portal_layout = QHBoxLayout()
        portal_layout.addWidget(QLabel("Portal:"))
        self.portal_input = QLineEdit()
        self.portal_input.setPlaceholderText("http://example.com:8080")
        portal_layout.addWidget(self.portal_input)
        config_layout.addLayout(portal_layout)

        # Credentials input
        creds_layout = QHBoxLayout()
        creds_layout.addWidget(QLabel("Credentials:"))
        self.creds_input = QTextEdit()
        self.creds_input.setPlaceholderText("Enter credentials in format username:password, one per line")
        self.creds_input.setMaximumHeight(100)
        creds_layout.addWidget(self.creds_input)
        config_layout.addLayout(creds_layout)

        # Buttons for loading credentials
        buttons_layout = QHBoxLayout()
        self.load_creds_button = QPushButton("Load Credentials")
        self.load_creds_button.clicked.connect(self.load_credentials)
        buttons_layout.addWidget(self.load_creds_button)

        self.clear_creds_button = QPushButton("Clear")
        self.clear_creds_button.clicked.connect(self.clear_credentials)
        buttons_layout.addWidget(self.clear_creds_button)

        self.remove_duplicates_creds_button = QPushButton("Remove Duplicates")
        self.remove_duplicates_creds_button.clicked.connect(self.remove_duplicates_creds)
        buttons_layout.addWidget(self.remove_duplicates_creds_button)
        config_layout.addLayout(buttons_layout)

        # Proxy settings
        proxy_layout = QHBoxLayout()
        self.use_proxy = QCheckBox("Use Proxy")
        proxy_layout.addWidget(self.use_proxy)

        self.load_proxy_button = QPushButton("Load Proxy List")
        self.load_proxy_button.clicked.connect(self.load_proxy_list)
        proxy_layout.addWidget(self.load_proxy_button)

        self.proxy_count_label = QLabel("Proxies: 0")
        proxy_layout.addWidget(self.proxy_count_label)

        proxy_layout.addStretch()
        config_layout.addLayout(proxy_layout)

        # Thread settings
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Max Threads:"))
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(100)
        self.thread_count.setValue(10)
        thread_layout.addWidget(self.thread_count)

        thread_layout.addWidget(QLabel("Timeout (sec):"))
        self.timeout = QSpinBox()
        self.timeout.setMinimum(1)
        self.timeout.setMaximum(30)
        self.timeout.setValue(5)
        thread_layout.addWidget(self.timeout)

        thread_layout.addStretch()
        config_layout.addLayout(thread_layout)

        # Auto save option
        self.auto_save = QCheckBox("Auto-save good results to file")
        self.auto_save.setChecked(True)
        config_layout.addWidget(self.auto_save)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        # Telegram settings
        self.telegram_settings = TelegramSettingsWidget('user_pass')
        layout.addWidget(self.telegram_settings)

        # Action buttons
        action_layout = QHBoxLayout()
        self.start_button = QPushButton("Start Check")
        self.start_button.clicked.connect(self.start_check)
        action_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("Stop Check")
        self.stop_button.clicked.connect(self.stop_check)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)

        self.clear_results_button = QPushButton("Clear Results")
        self.clear_results_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_results_button)

        layout.addLayout(action_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)

        # Results table
        self.results_table = QTableWidget(0, 7)
        self.results_table.setHorizontalHeaderLabels(["Portal", "Username", "Password", "Status", "Created", "Expires", "Max Conn"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.results_table)

        self.setLayout(layout)

    def load_default_values(self):
        """Load default values from config"""
        # Load settings from config
        self.thread_count.setValue(get_config('max_threads', 10))
        self.timeout.setValue(get_config('timeout', 5))
        self.auto_save.setChecked(get_config('auto_save', True))

    def connect_signals(self):
        """Connect signals from checker to UI"""
        self.checker.result_signal.connect(self.add_result)
        self.checker.progress_signal.connect(self.update_progress)
        self.checker.status_signal.connect(self.update_status)

        # Update checker settings when UI changes
        self.thread_count.valueChanged.connect(self.update_checker_settings)
        self.timeout.valueChanged.connect(self.update_checker_settings)
        self.auto_save.toggled.connect(self.update_checker_settings)
        self.telegram_settings.telegram_enabled.toggled.connect(self.update_checker_settings)
        self.telegram_settings.bot_token.textChanged.connect(self.update_checker_settings)
        self.telegram_settings.chat_id.textChanged.connect(self.update_checker_settings)

    def update_checker_settings(self):
        """Update checker settings from UI"""
        self.checker.max_threads = self.thread_count.value()
        self.checker.timeout = self.timeout.value()
        self.checker.auto_save = self.auto_save.isChecked()
        self.checker.telegram_enabled = self.telegram_settings.telegram_enabled.isChecked()
        self.checker.telegram_bot_token = self.telegram_settings.bot_token.text().strip()
        self.checker.telegram_chat_id = self.telegram_settings.chat_id.text().strip()

    def load_credentials(self):
        """Load credentials from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Credentials List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    creds = f.read()
                    self.creds_input.setText(creds)
                    self.status_label.setText(f"Loaded {len(creds.splitlines())} credentials from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load credentials: {str(e)}")

    def clear_credentials(self):
        """Clear credentials"""
        self.creds_input.clear()

    def remove_duplicates_creds(self):
        """Remove duplicate credentials from the list"""
        creds_text = self.creds_input.toPlainText().strip()
        if not creds_text:
            return

        # Get unique credentials while preserving order
        creds_list = [line.strip() for line in creds_text.splitlines() if line.strip()]
        unique_creds = []
        seen = set()
        for cred in creds_list:
            if cred.lower() not in seen:
                seen.add(cred.lower())
                unique_creds.append(cred)

        # Update the text area with unique credentials
        self.creds_input.setText("\n".join(unique_creds))

        # Show message about removed duplicates
        removed_count = len(creds_list) - len(unique_creds)
        if removed_count > 0:
            self.status_label.setText(f"Removed {removed_count} duplicate credentials")
        else:
            self.status_label.setText("No duplicate credentials found")

    def load_proxy_list(self):
        """Load proxy list from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Proxy List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                count = self.checker.load_proxies(file_path)
                self.proxy_count_label.setText(f"Proxies: {count}")
                self.status_label.setText(f"Loaded {count} proxies from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load proxy list: {str(e)}")

    def start_check(self):
        """Start credentials checking"""
        portal = self.portal_input.text().strip()
        if not portal:
            QMessageBox.warning(self, "Warning", "Please enter a portal URL.")
            return

        # Add http:// if missing
        if not portal.startswith('http://') and not portal.startswith('https://'):
            portal = f"http://{portal}"
            self.portal_input.setText(portal)

        # Get credentials list
        creds_text = self.creds_input.toPlainText().strip()
        if not creds_text:
            QMessageBox.warning(self, "Warning", "Please enter at least one credential pair.")
            return

        creds_list = [line.strip() for line in creds_text.splitlines() if line.strip()]

        # Update checker settings
        self.update_checker_settings()

        # Start checking
        success = self.checker.start_check(creds_list, portal, self.use_proxy.isChecked())

        if success:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("Checking...")
            self.progress_bar.setMaximum(len(creds_list))
            self.progress_bar.setValue(0)
        else:
            QMessageBox.critical(self, "Error", "Failed to start checking.")

    def stop_check(self):
        """Stop credentials checking"""
        self.checker.stop_check()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("Check stopped")

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)

    def add_result(self, result):
        """Add result to table"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(result.get('portal', '')))
        self.results_table.setItem(row, 1, QTableWidgetItem(result.get('username', '')))
        self.results_table.setItem(row, 2, QTableWidgetItem(result.get('password', '')))
        self.results_table.setItem(row, 3, QTableWidgetItem(result.get('status', '')))
        self.results_table.setItem(row, 4, QTableWidgetItem(result.get('created', '')))

        # Format expiration date if it's a timestamp
        exp_date = result.get('exp_date', '')
        if exp_date and exp_date.isdigit():
            from datetime import datetime
            try:
                exp_date = datetime.fromtimestamp(int(exp_date)).strftime('%Y-%m-%d')
            except:
                pass

        self.results_table.setItem(row, 5, QTableWidgetItem(exp_date))
        self.results_table.setItem(row, 6, QTableWidgetItem(str(result.get('max_connections', ''))))

        # Highlight row
        background_color = get_status_color("Active")
        text_color = get_text_color_for_background(background_color)

        for col in range(self.results_table.columnCount()):
            item = self.results_table.item(row, col)
            if item:
                item.setBackground(background_color)
                item.setForeground(text_color)

    def update_progress(self, current, total):
        """Update progress bar"""
        self.progress_bar.setValue(current)
        if current >= total:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText(f"Check completed. Found {self.results_table.rowCount()} valid credentials.")

    def update_status(self, status):
        """Update status label"""
        self.status_label.setText(status)

class IPTVTools(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("IPTV CHECKER - TWINS")
        self.setGeometry(100, 100, 1200, 800)

        # Main widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)

        # Create tab widget
        self.tabs = QTabWidget()
        self.main_layout.addWidget(self.tabs)

        # Create tabs
        print("Creating tabs...")
        try:
            self.mac_scanner_tab = MACTab()
            print("MAC Scanner tab created")
            self.user_pass_tab = UserPassTab()
            print("User-Pass tab created")
            self.m3u_checker_tab = QWidget()  # Will be implemented later
            print("M3U Checker tab created")
            self.stalker_player_tab = QWidget()  # Will be implemented later
            print("Stalker Player tab created")
            self.iptv_player_tab = QWidget()  # Will be implemented later
            print("IPTV Player tab created")
            try:
                self.tools_tab = ToolsTab()
                print("Tools tab created")
            except Exception as e:
                print(f"Error creating Tools tab: {e}")
                self.tools_tab = QWidget()

            try:
                self.xui_panel_tab = XUIPanelTab()
                print("XUI Panel tab created")
            except Exception as e:
                print(f"Error creating XUI Panel tab: {e}")
                self.xui_panel_tab = QWidget()

            try:
                self.portal_extractor_tab = PortalExtractorTab()
                print("Portal Extractor tab created")
            except Exception as e:
                print(f"Error creating Portal Extractor tab: {e}")
                self.portal_extractor_tab = QWidget()

            try:
                self.subdomain_finder_tab = SubdomainFinderTab()
                print("Subdomain Finder tab created")
            except Exception as e:
                print(f"Error creating Subdomain Finder tab: {e}")
                self.subdomain_finder_tab = QWidget()
        except Exception as e:
            print(f"Error creating tabs: {e}")

        # Add tabs to widget
        print("Adding tabs to widget...")
        try:
            self.tabs.addTab(self.mac_scanner_tab, "MAC SCANNER")
            self.tabs.addTab(self.user_pass_tab, "USER-PASS")
            self.tabs.addTab(self.m3u_checker_tab, "M3U CHECKER")
            self.tabs.addTab(self.xui_panel_tab, "XUI PANEL")
            self.tabs.addTab(self.portal_extractor_tab, "PORTAL EXTRACTOR")
            self.tabs.addTab(self.subdomain_finder_tab, "SUBDOMAIN FINDER")
            self.tabs.addTab(self.stalker_player_tab, "STALKER PLAYER")
            self.tabs.addTab(self.iptv_player_tab, "IPTV PLAYER")
            self.tabs.addTab(self.tools_tab, "TOOLS")
            print("All tabs added successfully")
        except Exception as e:
            print(f"Error adding tabs: {e}")

        # Initialize UI components
        self.init_ui()

    def init_ui(self):
        # Status bar
        self.statusBar().showMessage("Ready")

        # Menu bar
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('File')

        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Tools menu
        tools_menu = menubar.addMenu('Tools')

        settings_action = QAction('Settings', self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # Help menu
        help_menu = menubar.addMenu('Help')

        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def show_settings(self):
        """Show settings dialog"""
        QMessageBox.information(self, "Settings", "Settings dialog will be implemented in a future version.")

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About", "IPTV CHECKER - TWINS\n\nA comprehensive tool for IPTV management and testing.")

def main():
    print("Starting application...")
    app = QApplication(sys.argv)
    print("QApplication created")
    window = IPTVTools()
    print("IPTVTools window created")

    # Set window size and position explicitly
    window.resize(1024, 768)
    window.move(100, 100)

    window.show()
    print("Window shown")
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
