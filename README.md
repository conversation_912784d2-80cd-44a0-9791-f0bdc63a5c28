# IPTV Tools by <PERSON><PERSON><PERSON>nna

## Overview
A comprehensive tool for IPTV management and testing, including MAC scanner, User-Pass checker, M3U checker, Xtream Code checker, XUI Panel checker, and Portal extractor.

## Features
- **MAC Scanner**: Scan MAC addresses to find active ones
- **User-Pass Checker**: Check username and password combinations
- **M3U Checker**: Verify M3U playlist URLs
- **Xtream Code Checker**: Check Xtream Code credentials
- **XUI Panel Checker**: Check XUI panel admin credentials
- **Portal Extractor**: Extract and verify IPTV portals
- **Results Management**: Save, export, and manage results

## Additional Features
- Multi-threading support for faster scanning
- Proxy support for all checkers
- Automatic saving of good results during scanning
- Telegram integration for sending results to a Telegram bot
- Date filtering to exclude expired subscriptions

## Project Structure
1. **Core Processing Modules**:
   - `mac_scanner.py`: MAC address scanning functionality
   - `user_pass_checker.py`: User-Pass credential checking
   - `m3u_checker.py`: M3U playlist verification
   - `xtream_code_checker.py`: Xtream Code credential checking
   - `xui_panel_checker.py`: XUI Panel admin credential checking
   - `portal_extractor.py`: IPTV portal extraction and verification

2. **User Interfaces**:
   - `main.py`: Contains MACTab and UserPassTab definitions
   - `m3u_tab.py`: M3U checker interface
   - `xtream_code_tab.py`: Xtream Code checker interface
   - `xui_panel_tab.py`: XUI Panel checker interface
   - `portal_extractor_tab.py`: Portal extractor interface
   - `results_tab.py`: Results management interface
   - `about_tab.py`: About and activation interface

3. **Main Entry Point**:
   - `app.py`: Main application file

4. **Distribution Scripts**:
   - `package_windows.sh`: Create Windows executable package
   - `create_package.sh`: Create source code zip package

## Requirements
- Python 3.6+
- PyQt5
- Requests
- Python-telegram-bot

## Installation
### From Source
1. Clone or download the repository
2. Install required dependencies:
   ```
   pip install PyQt5 requests python-telegram-bot
   ```
3. Run the application:
   ```
   python src/app.py
   ```

### Windows Executable
1. Download the latest release
2. Extract the ZIP file
3. Run "IPTV Tools.exe"

## Usage
1. Select the desired tab for the function you want to use
2. Configure the settings as needed
3. Start the scan/check process
4. View and export results

## Telegram Integration
To use the Telegram integration:
1. Create a Telegram bot using BotFather
2. Get your bot token and chat ID
3. Enter these details in the Telegram settings section of each tab
4. Enable Telegram notifications

## License
This software is provided for educational purposes only. Use responsibly and in accordance with all applicable laws and regulations.

## Contact
- Official Telegram Group: @manzeraayenna
- Email: <EMAIL>
- Website: https://manzeraayenna.com
