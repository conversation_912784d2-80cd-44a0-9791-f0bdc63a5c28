import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QCheckBox, QSpinBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFileDialog, QProgressBar,
                            QGroupBox, QRadioButton, QMessageBox, QComboBox)
from PyQt5.QtGui import QColor
from PyQt5.QtCore import Qt, pyqtSlot
from styles import get_status_color

class MediaServerTab(QWidget):
    """Tab for Media Server (Emby/Jellyfin/Plex) functionality"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """Initialize UI components"""
        # Main layout
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)
        
        # Configuration section
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout()
        
        # Server type selection
        server_type_layout = QHBoxLayout()
        server_type_layout.addWidget(QLabel("Server Type:"))
        self.server_type = QComboBox()
        self.server_type.addItems(["Emby", "Jellyfin", "Plex"])
        server_type_layout.addWidget(self.server_type)
        config_layout.addLayout(server_type_layout)
        
        # Server URL input
        server_layout = QHBoxLayout()
        server_layout.addWidget(QLabel("Server URL:"))
        self.server_input = QLineEdit()
        self.server_input.setPlaceholderText("http://192.168.1.100:8096")
        server_layout.addWidget(self.server_input)
        config_layout.addLayout(server_layout)
        
        # Credentials section
        creds_layout = QHBoxLayout()
        
        # Username input
        creds_layout.addWidget(QLabel("Username:"))
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("admin")
        creds_layout.addWidget(self.username_input)
        
        # Password input
        creds_layout.addWidget(QLabel("Password:"))
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("password")
        self.password_input.setEchoMode(QLineEdit.Password)
        creds_layout.addWidget(self.password_input)
        
        # API key input
        creds_layout.addWidget(QLabel("API Key:"))
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("(Optional)")
        creds_layout.addWidget(self.api_key_input)
        
        config_layout.addLayout(creds_layout)
        
        # Options
        options_layout = QHBoxLayout()
        
        # Check libraries checkbox
        self.check_libraries = QCheckBox("Check Libraries")
        self.check_libraries.setChecked(True)
        self.check_libraries.setToolTip("Check libraries on the server")
        options_layout.addWidget(self.check_libraries)
        
        # Check users checkbox
        self.check_users = QCheckBox("Check Users")
        self.check_users.setChecked(True)
        self.check_users.setToolTip("Check users on the server")
        options_layout.addWidget(self.check_users)
        
        # Check plugins checkbox
        self.check_plugins = QCheckBox("Check Plugins")
        self.check_plugins.setChecked(True)
        self.check_plugins.setToolTip("Check plugins on the server")
        options_layout.addWidget(self.check_plugins)
        
        # Auto-save checkbox
        self.auto_save = QCheckBox("Auto-save Results")
        self.auto_save.setChecked(True)
        self.auto_save.setToolTip("Automatically save valid results to file")
        options_layout.addWidget(self.auto_save)
        
        config_layout.addLayout(options_layout)
        
        # Thread settings
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Max Threads:"))
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(50)
        self.thread_count.setValue(10)
        thread_layout.addWidget(self.thread_count)
        
        thread_layout.addWidget(QLabel("Timeout (sec):"))
        self.timeout = QSpinBox()
        self.timeout.setMinimum(1)
        self.timeout.setMaximum(30)
        self.timeout.setValue(10)
        thread_layout.addWidget(self.timeout)
        
        thread_layout.addStretch()
        config_layout.addLayout(thread_layout)
        
        config_group.setLayout(config_layout)
        main_layout.addWidget(config_group)
        
        # Action buttons
        action_layout = QHBoxLayout()
        
        # Test connection button
        self.test_button = QPushButton("Test Connection")
        self.test_button.clicked.connect(self.test_connection)
        action_layout.addWidget(self.test_button)
        
        # Start button
        self.start_button = QPushButton("Start Check")
        self.start_button.clicked.connect(self.start_check)
        action_layout.addWidget(self.start_button)
        
        # Stop button
        self.stop_button = QPushButton("Stop Check")
        self.stop_button.clicked.connect(self.stop_check)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)
        
        # Clear button
        self.clear_button = QPushButton("Clear Results")
        self.clear_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_button)
        
        main_layout.addLayout(action_layout)
        
        # Progress section
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(QLabel("Progress:"))
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        self.status_label = QLabel("Ready")
        progress_layout.addWidget(self.status_label)
        main_layout.addLayout(progress_layout)
        
        # Results table
        self.results_table = QTableWidget(0, 5)
        self.results_table.setHorizontalHeaderLabels(["Name", "Type", "Status", "Info", "URL"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        main_layout.addWidget(self.results_table)
        
        # Connect server type change to update placeholder
        self.server_type.currentIndexChanged.connect(self.update_placeholders)
        
    def update_placeholders(self):
        """Update placeholders based on server type"""
        server_type = self.server_type.currentText()
        if server_type == "Emby":
            self.server_input.setPlaceholderText("http://192.168.1.100:8096")
        elif server_type == "Jellyfin":
            self.server_input.setPlaceholderText("http://192.168.1.100:8096")
        elif server_type == "Plex":
            self.server_input.setPlaceholderText("http://192.168.1.100:32400")
    
    def test_connection(self):
        """Test connection to the media server"""
        # This is a placeholder for future implementation
        server_type = self.server_type.currentText()
        QMessageBox.information(self, "Information", f"{server_type} functionality will be implemented in a future update.")
    
    def start_check(self):
        """Start checking the media server"""
        # This is a placeholder for future implementation
        server_type = self.server_type.currentText()
        QMessageBox.information(self, "Information", f"{server_type} functionality will be implemented in a future update.")
    
    def stop_check(self):
        """Stop checking the media server"""
        # This is a placeholder for future implementation
        pass
    
    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)
        self.status_label.setText("Results cleared")
