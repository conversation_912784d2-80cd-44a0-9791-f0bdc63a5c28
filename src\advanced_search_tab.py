import os
import re
import glob
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QCheckBox, QSpinBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFileDialog, QProgressBar,
                            QGroupBox, QRadioButton, QMessageBox, QComboBox, QTabWidget,
                            QStackedWidget, QApplication)
from PyQt5.QtGui import QColor
from PyQt5.QtCore import Qt, pyqtSignal, QThread, pyqtSlot, QObject
from link_extractor import LinkExtractor

class LinkExtractionWorker(QObject):
    """Worker for extracting links from files"""
    finished = pyqtSignal(list, list)  # xtream_links, xui_links

    def __init__(self, extractor, folder_path, use_advanced=False):
        super().__init__()
        self.extractor = extractor
        self.folder_path = folder_path
        self.use_advanced = use_advanced

    def run(self):
        """Run the extraction process"""
        try:
            if self.use_advanced:
                xtream_links, xui_links, _ = self.extractor.extract_links_from_directory_method2(self.folder_path)
            else:
                xtream_links, xui_links, _ = self.extractor.extract_links_from_directory(self.folder_path)

            self.finished.emit(xtream_links, xui_links)
        except Exception as e:
            print(f"Error in extraction worker: {str(e)}")
            self.finished.emit([], [])

class SearchWorker(QThread):
    """Worker thread for searching files"""
    progress_signal = pyqtSignal(int, int)  # current, total
    result_signal = pyqtSignal(dict)  # search result
    finished_signal = pyqtSignal()  # search finished
    status_signal = pyqtSignal(str)  # status message

    def __init__(self, folder_path, search_patterns, search_mac=True, search_iptv=True, search_urls=True):
        super().__init__()
        self.folder_path = folder_path
        self.search_patterns = search_patterns
        self.search_mac = search_mac
        self.search_iptv = search_iptv
        self.search_urls = search_urls
        self.running = True

    def run(self):
        """Run the search"""
        try:
            # Get all text files in the folder
            file_paths = glob.glob(os.path.join(self.folder_path, "*.txt"))
            total_files = len(file_paths)

            if total_files == 0:
                self.status_signal.emit("No text files found in the selected folder.")
                self.finished_signal.emit()
                return

            self.status_signal.emit(f"Found {total_files} text files. Starting search...")

            # Process each file
            for i, file_path in enumerate(file_paths):
                if not self.running:
                    break

                self.status_signal.emit(f"Searching file {i+1}/{total_files}: {os.path.basename(file_path)}")
                self.progress_signal.emit(i+1, total_files)

                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    # Search for patterns
                    self._search_in_content(content, file_path)

                except Exception as e:
                    self.status_signal.emit(f"Error processing file {file_path}: {str(e)}")

            self.status_signal.emit("Search completed.")
            self.finished_signal.emit()

        except Exception as e:
            self.status_signal.emit(f"Error during search: {str(e)}")
            self.finished_signal.emit()

    def _search_in_content(self, content, file_path):
        """Search for patterns in file content"""
        # Search for MAC addresses
        if self.search_mac:
            mac_pattern = r'([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})'
            for match in re.finditer(mac_pattern, content):
                mac = match.group(0)
                line = self._get_line_context(content, match.start())
                result = {
                    'type': 'MAC Address',
                    'value': mac,
                    'file': os.path.basename(file_path),
                    'path': file_path,
                    'context': line
                }
                self.result_signal.emit(result)

        # Search for IPTV related patterns
        if self.search_iptv:
            # M3U URLs
            m3u_pattern = r'https?://[^\s<>"\']+\.m3u8?'
            for match in re.finditer(m3u_pattern, content):
                url = match.group(0)
                line = self._get_line_context(content, match.start())
                result = {
                    'type': 'M3U URL',
                    'value': url,
                    'file': os.path.basename(file_path),
                    'path': file_path,
                    'context': line
                }
                self.result_signal.emit(result)

            # Xtream codes patterns
            xtream_pattern = r'https?://[^\s<>"\']+/get\.php\?username=([^&]+)&password=([^&\s]+)'
            for match in re.finditer(xtream_pattern, content):
                url = match.group(0)
                line = self._get_line_context(content, match.start())
                result = {
                    'type': 'Xtream Codes',
                    'value': url,
                    'file': os.path.basename(file_path),
                    'path': file_path,
                    'context': line
                }
                self.result_signal.emit(result)

            # Portal URLs
            portal_pattern = r'https?://[^\s<>"\']+/c/|https?://[^\s<>"\']+/stalker_portal'
            for match in re.finditer(portal_pattern, content):
                url = match.group(0)
                line = self._get_line_context(content, match.start())
                result = {
                    'type': 'Portal URL',
                    'value': url,
                    'file': os.path.basename(file_path),
                    'path': file_path,
                    'context': line
                }
                self.result_signal.emit(result)

        # Search for custom patterns
        if self.search_patterns:
            for pattern in self.search_patterns:
                if pattern.strip():
                    try:
                        for match in re.finditer(pattern, content, re.IGNORECASE):
                            value = match.group(0)
                            line = self._get_line_context(content, match.start())
                            result = {
                                'type': 'Custom Pattern',
                                'value': value,
                                'file': os.path.basename(file_path),
                                'path': file_path,
                                'context': line
                            }
                            self.result_signal.emit(result)
                    except Exception as e:
                        self.status_signal.emit(f"Error with pattern '{pattern}': {str(e)}")

        # Search for URLs if enabled
        if self.search_urls:
            url_pattern = r'https?://[^\s<>"\']{8,}'
            for match in re.finditer(url_pattern, content):
                url = match.group(0)
                # Skip if already found as a specific type
                if (re.search(r'\.m3u8?$', url) or
                    re.search(r'/get\.php\?username=', url) or
                    re.search(r'/c/|/stalker_portal', url)):
                    continue

                # Skip unwanted domains/patterns that are not IPTV related
                unwanted_patterns = [
                    r'aliexpress\.com',
                    r'amazon\.com',
                    r'ebay\.com',
                    r'facebook\.com',
                    r'google\.com',
                    r'youtube\.com',
                    r'twitter\.com',
                    r'instagram\.com',
                    r'linkedin\.com',
                    r'github\.com',
                    r'stackoverflow\.com',
                    r'microsoft\.com',
                    r'apple\.com',
                    r'paypal\.com',
                    r'tumblr\.com',
                    r'reddit\.com',
                    r'wikipedia\.org',
                    r'dropbox\.com',
                    r'onedrive\.com',
                    r'drive\.google\.com'
                ]

                # Check if URL contains unwanted patterns
                is_unwanted = any(re.search(pattern, url, re.IGNORECASE) for pattern in unwanted_patterns)
                if is_unwanted:
                    continue

                # Only include URLs that might be IPTV related
                iptv_indicators = [
                    r'iptv', r'stream', r'tv', r'media', r'panel', r'server',
                    r'player', r'portal', r'api', r'xmltv', r'epg',
                    r'\.m3u', r'\.ts', r'\.mp4', r'\.mkv'
                ]

                # Check if URL contains IPTV indicators or is an IP address
                is_iptv_related = (
                    any(re.search(indicator, url, re.IGNORECASE) for indicator in iptv_indicators) or
                    re.search(r'(?:\d{1,3}\.){3}\d{1,3}', url)  # IP address pattern
                )

                # Only emit if it looks IPTV related
                if is_iptv_related:
                    line = self._get_line_context(content, match.start())
                    result = {
                        'type': 'URL',
                        'value': url,
                        'file': os.path.basename(file_path),
                        'path': file_path,
                        'context': line
                    }
                    self.result_signal.emit(result)

    def _get_line_context(self, content, position):
        """Get the line containing the match"""
        start = max(0, content.rfind('\n', 0, position) + 1)
        end = content.find('\n', position)
        if end == -1:
            end = len(content)
        return content[start:end].strip()

    def stop(self):
        """Stop the search"""
        self.running = False

class AdvancedSearchTab(QWidget):
    """Tab for advanced search functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_worker = None
        self.link_extractor = LinkExtractor()
        self.init_ui()

    def init_ui(self):
        """Initialize UI components"""
        # Main layout
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)

        # Create tab widget for different search modes
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # Create search tab
        search_tab = QWidget()
        self.tab_widget.addTab(search_tab, "Pattern Search")

        # Create link extractor tab
        link_extractor_tab = QWidget()
        self.tab_widget.addTab(link_extractor_tab, "Link Extractor")

        # Setup search tab
        self.setup_search_tab(search_tab)

        # Setup link extractor tab
        self.setup_link_extractor_tab(link_extractor_tab)

    def setup_search_tab(self, tab):
        """Setup the pattern search tab"""
        # Main layout for search tab
        search_layout = QVBoxLayout()
        tab.setLayout(search_layout)

        # Folder selection section
        folder_group = QGroupBox("Search Location")
        folder_layout = QVBoxLayout()

        folder_input_layout = QHBoxLayout()
        folder_input_layout.addWidget(QLabel("Folder:"))
        self.folder_input = QLineEdit()
        self.folder_input.setPlaceholderText("Select a folder containing text files to search")
        folder_input_layout.addWidget(self.folder_input)

        browse_button = QPushButton("Browse")
        browse_button.clicked.connect(self.browse_folder)
        folder_input_layout.addWidget(browse_button)

        folder_layout.addLayout(folder_input_layout)
        folder_group.setLayout(folder_layout)
        search_layout.addWidget(folder_group)

        # Search options section
        options_group = QGroupBox("Search Options")
        options_layout = QVBoxLayout()

        # Search types
        types_layout = QHBoxLayout()
        self.search_mac = QCheckBox("MAC Addresses")
        self.search_mac.setChecked(True)
        types_layout.addWidget(self.search_mac)

        self.search_iptv = QCheckBox("IPTV URLs")
        self.search_iptv.setChecked(True)
        types_layout.addWidget(self.search_iptv)

        self.search_urls = QCheckBox("Other URLs")
        self.search_urls.setChecked(True)
        types_layout.addWidget(self.search_urls)

        options_layout.addLayout(types_layout)

        # Custom patterns
        patterns_layout = QVBoxLayout()
        patterns_layout.addWidget(QLabel("Custom Search Patterns (one per line, regex supported):"))
        self.patterns_input = QTextEdit()
        self.patterns_input.setPlaceholderText("Enter custom search patterns, one per line\nExample: username=([^&]+)&password=([^&\\s]+)")
        self.patterns_input.setMaximumHeight(80)
        patterns_layout.addWidget(self.patterns_input)

        options_layout.addLayout(patterns_layout)
        options_group.setLayout(options_layout)
        search_layout.addWidget(options_group)

        # Action buttons
        action_layout = QHBoxLayout()

        # Start button
        self.start_button = QPushButton("Start Search")
        self.start_button.clicked.connect(self.start_search)
        action_layout.addWidget(self.start_button)

        # Stop button
        self.stop_button = QPushButton("Stop Search")
        self.stop_button.clicked.connect(self.stop_search)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)

        # Clear button
        self.clear_button = QPushButton("Clear Results")
        self.clear_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_button)

        # Remove Duplicates button
        self.remove_duplicates_button = QPushButton("Remove Duplicates")
        self.remove_duplicates_button.clicked.connect(self.remove_duplicates)
        action_layout.addWidget(self.remove_duplicates_button)

        # Export button
        self.export_button = QPushButton("Export Results")
        self.export_button.clicked.connect(self.export_results)
        action_layout.addWidget(self.export_button)

        search_layout.addLayout(action_layout)

        # Progress section
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(QLabel("Progress:"))
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        self.status_label = QLabel("Ready")
        progress_layout.addWidget(self.status_label)
        search_layout.addLayout(progress_layout)

        # Results table
        self.results_table = QTableWidget(0, 5)
        self.results_table.setHorizontalHeaderLabels(["Type", "Value", "File", "Context", "Path"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.results_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.results_table.customContextMenuRequested.connect(self.show_context_menu)
        search_layout.addWidget(self.results_table)

    def setup_link_extractor_tab(self, tab):
        """Setup the link extractor tab"""
        # Main layout for link extractor tab
        extractor_layout = QVBoxLayout()
        tab.setLayout(extractor_layout)

        # Folder selection section
        folder_group = QGroupBox("Extract Links From")
        folder_layout = QVBoxLayout()

        folder_input_layout = QHBoxLayout()
        folder_input_layout.addWidget(QLabel("Folder:"))
        self.extractor_folder_input = QLineEdit()
        self.extractor_folder_input.setPlaceholderText("Select a folder containing text files to extract links from")
        folder_input_layout.addWidget(self.extractor_folder_input)

        browse_button = QPushButton("Browse")
        browse_button.clicked.connect(self.browse_extractor_folder)
        folder_input_layout.addWidget(browse_button)

        folder_layout.addLayout(folder_input_layout)
        folder_group.setLayout(folder_layout)
        extractor_layout.addWidget(folder_group)

        # Extraction options
        options_group = QGroupBox("Extraction Options")
        options_layout = QVBoxLayout()

        # Method selection
        method_layout = QHBoxLayout()
        method_layout.addWidget(QLabel("Extraction Method:"))
        self.method_combo = QComboBox()
        self.method_combo.addItems(["Standard", "Advanced (for large files)"])
        method_layout.addWidget(self.method_combo)
        method_layout.addStretch()
        options_layout.addLayout(method_layout)

        # Output options
        output_layout = QHBoxLayout()
        self.auto_save_cb = QCheckBox("Auto-save extracted links to files")
        self.auto_save_cb.setChecked(True)
        output_layout.addWidget(self.auto_save_cb)
        output_layout.addStretch()
        options_layout.addLayout(output_layout)

        options_group.setLayout(options_layout)
        extractor_layout.addWidget(options_group)

        # Action buttons
        action_layout = QHBoxLayout()

        # Start button
        self.extract_start_btn = QPushButton("Start Extraction")
        self.extract_start_btn.clicked.connect(self.start_extraction)
        self.extract_start_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; font-size: 11pt; padding: 8px 15px; border-radius: 4px;")
        action_layout.addWidget(self.extract_start_btn)

        # Clear button
        self.extract_clear_btn = QPushButton("Clear Results")
        self.extract_clear_btn.clicked.connect(self.clear_extraction_results)
        action_layout.addWidget(self.extract_clear_btn)

        # Export button
        self.extract_export_btn = QPushButton("Export Results")
        self.extract_export_btn.clicked.connect(self.export_extraction_results)
        action_layout.addWidget(self.extract_export_btn)

        extractor_layout.addLayout(action_layout)

        # Progress section
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(QLabel("Progress:"))
        self.extract_progress_bar = QProgressBar()
        progress_layout.addWidget(self.extract_progress_bar)
        self.extract_status_label = QLabel("Ready")
        progress_layout.addWidget(self.extract_status_label)
        extractor_layout.addLayout(progress_layout)

        # Results section
        results_group = QGroupBox("Extraction Results")
        results_layout = QVBoxLayout()

        # Results table
        self.extract_results_table = QTableWidget(0, 4)
        self.extract_results_table.setHorizontalHeaderLabels(["File", "Xtream Links", "XUI Links", "Total Links"])
        self.extract_results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        results_layout.addWidget(self.extract_results_table)

        # Summary section
        summary_layout = QHBoxLayout()
        self.xtream_count_label = QLabel("Xtream Links: 0")
        self.xui_count_label = QLabel("XUI Links: 0")
        self.total_count_label = QLabel("Total Links: 0")
        summary_layout.addWidget(self.xtream_count_label)
        summary_layout.addWidget(self.xui_count_label)
        summary_layout.addWidget(self.total_count_label)
        summary_layout.addStretch()
        results_layout.addLayout(summary_layout)

        results_group.setLayout(results_layout)
        extractor_layout.addWidget(results_group)

        # Connect signals from link extractor
        self.link_extractor.progress_signal.connect(self.update_extraction_progress)
        self.link_extractor.status_signal.connect(self.update_extraction_status)
        self.link_extractor.result_signal.connect(self.add_extraction_result)

    def browse_folder(self):
        """Browse for folder containing text files"""
        folder_path = QFileDialog.getExistingDirectory(self, "Select Folder")
        if folder_path:
            self.folder_input.setText(folder_path)

    def browse_extractor_folder(self):
        """Browse for folder containing text files for link extraction"""
        folder_path = QFileDialog.getExistingDirectory(self, "Select Folder for Link Extraction")
        if folder_path:
            self.extractor_folder_input.setText(folder_path)

    def start_search(self):
        """Start searching for IPTV/MAC information"""
        folder_path = self.folder_input.text().strip()
        if not folder_path:
            QMessageBox.warning(self, "Warning", "Please select a folder to search.")
            return

        if not os.path.isdir(folder_path):
            QMessageBox.warning(self, "Warning", f"The path '{folder_path}' is not a valid directory.")
            return

        # Get custom patterns
        patterns_text = self.patterns_input.toPlainText().strip()
        patterns = [p.strip() for p in patterns_text.splitlines() if p.strip()]

        # Clear previous results
        self.clear_results()

        # Create and start worker thread
        self.search_worker = SearchWorker(
            folder_path,
            patterns,
            self.search_mac.isChecked(),
            self.search_iptv.isChecked(),
            self.search_urls.isChecked()
        )
        self.search_worker.progress_signal.connect(self.update_progress)
        self.search_worker.result_signal.connect(self.add_result)
        self.search_worker.finished_signal.connect(self.search_finished)
        self.search_worker.status_signal.connect(self.update_status)

        self.search_worker.start()

        # Update UI
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("Searching...")

    def stop_search(self):
        """Stop the search"""
        if self.search_worker and self.search_worker.isRunning():
            self.search_worker.stop()
            self.status_label.setText("Stopping search...")

    def search_finished(self):
        """Handle search completion"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText(f"Search completed. Found {self.results_table.rowCount()} results.")

    def update_progress(self, current, total):
        """Update progress bar"""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)

    def update_status(self, status):
        """Update status label"""
        self.status_label.setText(status)

    def add_result(self, result):
        """Add result to table"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(result['type']))
        self.results_table.setItem(row, 1, QTableWidgetItem(result['value']))
        self.results_table.setItem(row, 2, QTableWidgetItem(result['file']))
        self.results_table.setItem(row, 3, QTableWidgetItem(result['context']))
        self.results_table.setItem(row, 4, QTableWidgetItem(result['path']))

        # Color code by type
        color = QColor(255, 255, 255)  # Default white
        if result['type'] == 'MAC Address':
            color = QColor(200, 230, 255)  # Light blue
        elif result['type'] == 'M3U URL':
            color = QColor(200, 255, 200)  # Light green
        elif result['type'] == 'Xtream Codes':
            color = QColor(255, 230, 200)  # Light orange
        elif result['type'] == 'Portal URL':
            color = QColor(230, 200, 255)  # Light purple

        for col in range(self.results_table.columnCount()):
            item = self.results_table.item(row, col)
            if item:
                item.setBackground(color)

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)
        self.progress_bar.setValue(0)
        self.status_label.setText("Ready")

    def remove_duplicates(self):
        """Remove duplicate entries from results table"""
        if self.results_table.rowCount() == 0:
            self.status_label.setText("No results to process")
            return

        # Get all values from the table
        values = []
        for row in range(self.results_table.rowCount()):
            row_data = {}
            for col in range(self.results_table.columnCount()):
                item = self.results_table.item(row, col)
                if item:
                    row_data[col] = item.text()
                else:
                    row_data[col] = ""
            values.append(row_data)

        # Find unique values (based on Value column and Type column)
        unique_values = {}
        for data in values:
            # Use Type (column 0) + Value (column 1) as the key for uniqueness
            key = data.get(0, "") + "|" + data.get(1, "")
            if key and key not in unique_values:
                unique_values[key] = data

        # Clear table and add unique values
        self.results_table.setRowCount(0)
        for data in unique_values.values():
            row = self.results_table.rowCount()
            self.results_table.insertRow(row)

            for col, text in data.items():
                self.results_table.setItem(row, col, QTableWidgetItem(text))

            # Apply color coding
            type_text = data.get(0, "")
            color = QColor(255, 255, 255)  # Default white
            if type_text == 'MAC Address':
                color = QColor(200, 230, 255)  # Light blue
            elif type_text == 'M3U URL':
                color = QColor(200, 255, 200)  # Light green
            elif type_text == 'Xtream Codes':
                color = QColor(255, 230, 200)  # Light orange
            elif type_text == 'Portal URL':
                color = QColor(230, 200, 255)  # Light purple

            for col in range(self.results_table.columnCount()):
                item = self.results_table.item(row, col)
                if item:
                    item.setBackground(color)

        # Update status
        removed = len(values) - len(unique_values)
        self.status_label.setText(f"Removed {removed} duplicate entries. {len(unique_values)} unique entries remaining.")

    def export_results(self):
        """Export results to file"""
        if self.results_table.rowCount() == 0:
            QMessageBox.information(self, "Information", "No results to export.")
            return

        file_path, _ = QFileDialog.getSaveFileName(self, "Save Results", "", "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)")
        if not file_path:
            return

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                # Determine file type
                if file_path.lower().endswith('.csv'):
                    # CSV format
                    f.write("Type,Value,File,Context,Path\n")
                    for row in range(self.results_table.rowCount()):
                        row_data = []
                        for col in range(self.results_table.columnCount()):
                            item = self.results_table.item(row, col)
                            text = item.text() if item else ""
                            # Escape quotes and commas for CSV
                            text = text.replace('"', '""')
                            row_data.append(f'"{text}"')
                        f.write(','.join(row_data) + '\n')
                else:
                    # Text format
                    for row in range(self.results_table.rowCount()):
                        type_item = self.results_table.item(row, 0)
                        value_item = self.results_table.item(row, 1)
                        file_item = self.results_table.item(row, 2)
                        context_item = self.results_table.item(row, 3)

                        type_text = type_item.text() if type_item else "Unknown"
                        value_text = value_item.text() if value_item else ""
                        file_text = file_item.text() if file_item else ""
                        context_text = context_item.text() if context_item else ""

                        f.write(f"Type: {type_text}\n")
                        f.write(f"Value: {value_text}\n")
                        f.write(f"File: {file_text}\n")
                        f.write(f"Context: {context_text}\n")
                        f.write("-" * 50 + "\n")

            QMessageBox.information(self, "Success", f"Results exported to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    def start_extraction(self):
        """Start extracting links from text files"""
        folder_path = self.extractor_folder_input.text().strip()
        if not folder_path:
            QMessageBox.warning(self, "Warning", "Please select a folder to extract links from.")
            return

        if not os.path.isdir(folder_path):
            QMessageBox.warning(self, "Warning", f"The path '{folder_path}' is not a valid directory.")
            return

        # Clear previous results
        self.clear_extraction_results()

        # Update UI
        self.extract_start_btn.setEnabled(False)
        self.extract_status_label.setText("Extracting links...")

        # Use advanced method if selected
        use_advanced = self.method_combo.currentIndex() == 1

        # Start extraction in a separate thread
        self.extraction_thread = QThread()
        self.extraction_worker = LinkExtractionWorker(
            self.link_extractor,
            folder_path,
            use_advanced
        )
        self.extraction_worker.moveToThread(self.extraction_thread)

        # Connect signals
        self.extraction_thread.started.connect(self.extraction_worker.run)
        self.extraction_worker.finished.connect(self.extraction_thread.quit)
        self.extraction_worker.finished.connect(self.extraction_worker.deleteLater)
        self.extraction_thread.finished.connect(self.extraction_thread.deleteLater)
        self.extraction_worker.finished.connect(self.extraction_finished)

        # Start the thread
        self.extraction_thread.start()

    def extraction_finished(self, xtream_links, xui_links):
        """Handle extraction completion"""
        self.extract_start_btn.setEnabled(True)

        # Update summary labels
        self.xtream_count_label.setText(f"Xtream Links: {len(xtream_links)}")
        self.xui_count_label.setText(f"XUI Links: {len(xui_links)}")
        self.total_count_label.setText(f"Total Links: {len(xtream_links) + len(xui_links)}")

        # Auto-save if enabled
        if self.auto_save_cb.isChecked() and (xtream_links or xui_links):
            xtream_file = os.path.join(os.path.dirname(self.extractor_folder_input.text()), "extracted_xtream_links.txt")
            xui_file = os.path.join(os.path.dirname(self.extractor_folder_input.text()), "extracted_xui_links.txt")

            xtream_count, xui_count = self.link_extractor.save_links_to_files(xtream_file, xui_file)

            if xtream_count > 0 or xui_count > 0:
                self.extract_status_label.setText(f"Extraction completed. Saved {xtream_count} Xtream links and {xui_count} XUI links to files.")
                QMessageBox.information(self, "Success", f"Saved {xtream_count} Xtream links to {xtream_file}\nSaved {xui_count} XUI links to {xui_file}")
        else:
            self.extract_status_label.setText(f"Extraction completed. Found {len(xtream_links)} Xtream links and {len(xui_links)} XUI links.")

    def update_extraction_progress(self, current, total):
        """Update extraction progress bar"""
        self.extract_progress_bar.setMaximum(total)
        self.extract_progress_bar.setValue(current)

    def update_extraction_status(self, status):
        """Update extraction status label"""
        self.extract_status_label.setText(status)

    def add_extraction_result(self, result):
        """Add extraction result to table"""
        row = self.extract_results_table.rowCount()
        self.extract_results_table.insertRow(row)

        self.extract_results_table.setItem(row, 0, QTableWidgetItem(result['file_name']))
        self.extract_results_table.setItem(row, 1, QTableWidgetItem(str(result['xtream_count'])))
        self.extract_results_table.setItem(row, 2, QTableWidgetItem(str(result['xui_count'])))
        self.extract_results_table.setItem(row, 3, QTableWidgetItem(str(result['total_links'])))

        # Color code based on number of links found
        if result['total_links'] > 0:
            color = QColor(200, 255, 200)  # Light green
        else:
            color = QColor(255, 255, 255)  # White

        for col in range(self.extract_results_table.columnCount()):
            item = self.extract_results_table.item(row, col)
            if item:
                item.setBackground(color)

    def clear_extraction_results(self):
        """Clear extraction results"""
        self.extract_results_table.setRowCount(0)
        self.extract_progress_bar.setValue(0)
        self.extract_status_label.setText("Ready")
        self.xtream_count_label.setText("Xtream Links: 0")
        self.xui_count_label.setText("XUI Links: 0")
        self.total_count_label.setText("Total Links: 0")

    def export_extraction_results(self):
        """Export extraction results"""
        if self.extract_results_table.rowCount() == 0:
            QMessageBox.information(self, "Information", "No results to export.")
            return

        # Get links from the extractor
        xtream_links = self.link_extractor.xtream_links
        xui_links = self.link_extractor.xui_links

        if not xtream_links and not xui_links:
            QMessageBox.information(self, "Information", "No links to export.")
            return

        # Ask for export location
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Links",
            "",
            "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)"
        )

        if not file_path:
            return

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                # Determine file type
                if file_path.lower().endswith('.csv'):
                    # CSV format
                    f.write("Type,URL\n")
                    for link in xtream_links:
                        f.write(f"Xtream,{link}\n")
                    for link in xui_links:
                        f.write(f"XUI,{link}\n")
                else:
                    # Text format
                    f.write("=== Xtream Code Links ===\n")
                    for link in xtream_links:
                        f.write(f"{link}\n")
                    f.write("\n=== XUI Panel Links ===\n")
                    for link in xui_links:
                        f.write(f"{link}\n")

            QMessageBox.information(self, "Success", f"Links exported to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to export links: {str(e)}")

    def show_context_menu(self, position):
        """Show context menu for results table"""
        menu = QApplication.instance().createPopupMenu()

        # Add custom actions
        copy_action = menu.addAction("Copy Value")
        copy_action.triggered.connect(self.copy_selected_value)

        open_file_action = menu.addAction("Open Containing File")
        open_file_action.triggered.connect(self.open_containing_file)

        menu.exec_(self.results_table.mapToGlobal(position))

    def copy_selected_value(self):
        """Copy selected value to clipboard"""
        selected_items = self.results_table.selectedItems()
        if not selected_items:
            return

        # Get the value from the second column (Value)
        row = selected_items[0].row()
        value_item = self.results_table.item(row, 1)
        if value_item:
            QApplication.clipboard().setText(value_item.text())
            self.status_label.setText("Value copied to clipboard")

    def open_containing_file(self):
        """Open the file containing the selected result"""
        selected_items = self.results_table.selectedItems()
        if not selected_items:
            return

        # Get the file path from the fifth column (Path)
        row = selected_items[0].row()
        path_item = self.results_table.item(row, 4)
        if path_item and os.path.exists(path_item.text()):
            os.startfile(path_item.text())
        else:
            QMessageBox.warning(self, "Warning", "Could not open the file.")
