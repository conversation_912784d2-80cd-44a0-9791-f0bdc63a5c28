"""
Subdomain Finder module for IPTV Tools.
This module discovers subdomains for a given domain using various methods.
"""

import argparse
import requests
import dns.resolver
import socket
import re
import os
import sys
import threading
from queue import Queue
import time
from urllib.parse import urlparse
import logging
from PyQt5.QtCore import QObject, pyqtSignal

# Get logger
logger = logging.getLogger("iptv_checker.subdomain_finder")

# --- Configuration ---
# Use a small built-in wordlist for brute-forcing
DEFAULT_WORDLIST = [
    "www", "mail", "ftp", "localhost", "webmail", "smtp", "pop", "ns1", "ns2", "admin",
    "test", "dev", "webdisk", "cpanel", "portal", "blog", "shop", "api", "vpn", "tv",
    "support", "docs", "intranet", "iptv", "secure", "internal", "remote", "files", "cloud",
    "staging", "beta", "app", "store", "email", "news", "forum", "help", "status", "wiki",
    "backup", "db", "sql", "mysql", "STB", "mac_address", "portal", "stalker_portal", "docker", "mac"
]
BRUTEFORCE_THREADS = 20
REQUEST_TIMEOUT = 5
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

class SubdomainFinder(QObject):
    """Class for finding subdomains of a given domain"""

    # Define signals
    result_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, int)
    status_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(list)

    def __init__(self):
        super().__init__()
        self.running = False
        self.found_subdomains_set = set()
        self.found_subdomains_lock = threading.Lock()
        self.wordlist = DEFAULT_WORDLIST
        self.threads = BRUTEFORCE_THREADS
        self.timeout = REQUEST_TIMEOUT
        self.methods = ['brute', 'crtsh']
        self.base_domain = ""
        self.output_file = ""
        self.telegram_enabled = False
        self.telegram_bot_token = ""
        self.telegram_chat_id = ""

    def print_status(self, message):
        """Print status message and emit signal"""
        logger.info(message)
        self.status_signal.emit(message)

    def print_success(self, message):
        """Print success message and emit signal"""
        logger.info(message)
        self.status_signal.emit(message)

    def print_error(self, message):
        """Print error message and emit signal"""
        logger.error(message)
        self.status_signal.emit(f"Error: {message}")

    def extract_domain(self, target_url):
        """Extracts the base domain name from a URL or domain string."""
        if not target_url.startswith(("http://", "https://")):
            target_url = "http://" + target_url  # Add scheme for parsing

        parsed_uri = urlparse(target_url)
        domain = parsed_uri.netloc

        # Remove port if present
        domain = domain.split(':')[0]

        # Basic validation (simplified)
        if not re.match(r"^([a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,})$", domain):
             # Try removing potential leading 'www.' if initial parse failed
             if domain.startswith("www."):
                 domain = domain[4:]
                 if re.match(r"^([a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,})$", domain):
                     return domain
             self.print_error(f"Invalid domain format extracted: {parsed_uri.netloc}")
             return None

        # Handle cases like www.google.com -> google.com (optional, depends on desired behavior)
        # For subdomain finding, we usually want the registered domain part.
        # This part is tricky, using public suffix list is the robust way.
        # For simplicity, we'll keep it as is for now, e.g., www.google.com
        # Let's refine: we want the base domain like 'google.com' for appending subdomains.
        parts = domain.split('.')
        if len(parts) > 2:
            # Simple check, might fail for co.uk etc. A public suffix list is better.
            # Assuming standard TLDs for now.
            is_ip = all(part.isdigit() for part in parts)
            if not is_ip:
                 # Check if the last two parts form a common structure like co.uk, com.au
                 potential_base = parts[-2] + '.' + parts[-1]
                 # Very basic check for common TLDs, needs improvement for robustness
                 common_second_level = ['co', 'com', 'org', 'net', 'gov', 'edu']
                 if parts[-2] in common_second_level and len(parts) > 2:
                     if len(parts) > 3: # e.g. www.company.co.uk
                         return '.'.join(parts[-3:]) # company.co.uk
                     else: # e.g. company.co.uk
                         return '.'.join(parts[-2:]) # Return as is
                 elif len(parts) > 2: # e.g. www.google.com
                     return '.'.join(parts[-2:]) # google.com
                 else: # e.g. google.com
                     return domain
            else:
                return domain # Return IP address as is
        else:
            return domain # e.g., google.com

    def resolve_subdomain(self, subdomain):
        """Attempts to resolve a subdomain using DNS."""
        try:
            # Use socket.gethostbyname for a simple A record check
            socket.gethostbyname(subdomain)
            with self.found_subdomains_lock:
                if subdomain not in self.found_subdomains_set:
                    self.print_success(f"Found (Brute-force): {subdomain}")
                    self.result_signal.emit(subdomain)
                    self.found_subdomains_set.add(subdomain)
                    return True
        except socket.gaierror:
            pass # Subdomain does not resolve
        except Exception as e:
            # self.print_error(f"Error resolving {subdomain}: {e}")
            pass # Ignore other potential errors for cleaner output
        return False

    def brute_force_subdomains(self):
        """Performs brute-force subdomain discovery using a wordlist."""
        self.print_status(f"Starting brute-force discovery with {len(self.wordlist)} words...")
        q = Queue()

        for word in self.wordlist:
            subdomain = f"{word}.{self.base_domain}"
            q.put(subdomain)

        threads = []
        thread_count = min(self.threads, q.qsize())
        for _ in range(thread_count):
            t = threading.Thread(target=lambda: self.worker(q), daemon=True)
            threads.append(t)
            t.start()

        # Update progress periodically
        total_words = q.qsize()
        while not q.empty() and self.running:
            remaining = q.qsize()
            progress = total_words - remaining
            self.progress_signal.emit(progress, total_words)
            time.sleep(0.5)

        q.join() # Wait for all tasks in the queue to be processed
        self.print_status("Brute-force discovery finished.")

    def worker(self, q):
        """Worker thread for brute-force resolution."""
        while not q.empty() and self.running:
            subdomain = q.get()
            self.resolve_subdomain(subdomain)
            q.task_done()

    def search_crtsh(self):
        """Searches for subdomains using crt.sh certificate transparency logs."""
        self.print_status("Querying crt.sh...")
        url = f"https://crt.sh/?q=%.{self.base_domain}&output=json"
        headers = {'User-Agent': USER_AGENT}
        found_count = 0
        try:
            response = requests.get(url, headers=headers, timeout=self.timeout * 3) # Longer timeout for crt.sh
            response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)

            if response.text.strip(): # Check if response is not empty
                try:
                    data = response.json()
                except requests.exceptions.JSONDecodeError:
                    self.print_error("Failed to decode JSON response from crt.sh. Response might be HTML (e.g., rate limit page).")
                    self.print_error(f"Response text: {response.text[:200]}...") # Print beginning of response
                    return
            else:
                self.print_status("Received empty response from crt.sh.")
                return

            subdomains_crt = set()
            for entry in data:
                name_value = entry.get('name_value', '')
                # Split potential multiple domains on newline and filter
                for name in name_value.split('\n'):
                    name = name.strip()
                    # Check if it's a valid subdomain of the base_domain
                    if name.endswith(f".{self.base_domain}") and name != self.base_domain:
                        # Remove wildcard prefix if present
                        if name.startswith('*.'):
                            name = name[2:]
                        # Add to our set (handles duplicates)
                        subdomains_crt.add(name.lower())

            with self.found_subdomains_lock:
                initial_count = len(self.found_subdomains_set)
                for subdomain in subdomains_crt:
                    if subdomain not in self.found_subdomains_set:
                        self.found_subdomains_set.add(subdomain)
                        self.result_signal.emit(subdomain)
                found_count = len(self.found_subdomains_set) - initial_count

            self.print_success(f"Found {found_count} new unique subdomains via crt.sh.")

        except requests.exceptions.RequestException as e:
            self.print_error(f"Error querying crt.sh: {e}")
        except Exception as e:
            self.print_error(f"An unexpected error occurred during crt.sh search: {e}")

    def start_scan(self, target_domain, methods=None, custom_wordlist=None, threads=None, timeout=None):
        """Start subdomain discovery"""
        if self.running:
            return False

        # Initialize scan
        self.running = True
        self.found_subdomains_set.clear()

        # Set parameters
        self.base_domain = self.extract_domain(target_domain)
        if not self.base_domain:
            self.print_error("Invalid domain format")
            self.running = False
            return False

        if methods:
            self.methods = methods
        if custom_wordlist:
            self.wordlist = custom_wordlist
        if threads:
            self.threads = threads
        if timeout:
            self.timeout = timeout

        # Set output file
        self.output_file = f"{self.base_domain}_subdomains.txt"

        self.print_status(f"Target base domain: {self.base_domain}")
        self.print_status(f"Output file: {self.output_file}")

        # Start discovery in separate threads
        discovery_threads = []

        if 'brute' in self.methods:
            brute_thread = threading.Thread(target=self.brute_force_subdomains, daemon=True)
            discovery_threads.append(brute_thread)
            brute_thread.start()

        if 'crtsh' in self.methods:
            crtsh_thread = threading.Thread(target=self.search_crtsh, daemon=True)
            discovery_threads.append(crtsh_thread)
            crtsh_thread.start()

        # Start monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_progress, args=(discovery_threads,), daemon=True)
        monitor_thread.start()

        return True

    def stop_scan(self):
        """Stop subdomain discovery"""
        if not self.running:
            return False

        self.running = False
        self.print_status("Stopping subdomain discovery...")
        return True

    def monitor_progress(self, threads):
        """Monitor progress and emit signals"""
        # Wait for all discovery threads to complete
        for t in threads:
            t.join()

        if not self.running:
            self.print_status("Subdomain discovery stopped.")
            return

        self.print_status("Discovery phase complete.")

        # Save results
        if self.found_subdomains_set:
            self.print_status(f"Found a total of {len(self.found_subdomains_set)} unique subdomains.")
            sorted_subdomains = sorted(list(self.found_subdomains_set))
            try:
                with open(self.output_file, 'w') as f:
                    for sub in sorted_subdomains:
                        f.write(sub + '\n')
                self.print_success(f"Results saved to {self.output_file}")
            except Exception as e:
                self.print_error(f"Failed to write results to {self.output_file}: {e}")
        else:
            self.print_status("No subdomains found.")
            # Create an empty file
            try:
                open(self.output_file, 'w').close()
                self.print_status(f"Created empty results file: {self.output_file}")
            except Exception as e:
                self.print_error(f"Failed to create empty results file {self.output_file}: {e}")

        # Emit finished signal with results
        self.finished_signal.emit(sorted(list(self.found_subdomains_set)))
        self.running = False

    def load_wordlist(self, file_path):
        """Load wordlist from file"""
        try:
            with open(file_path, 'r') as f:
                wordlist = [line.strip() for line in f if line.strip()]
            if not wordlist:
                self.print_error("Custom wordlist is empty.")
                return False
            self.wordlist = wordlist
            self.print_status(f"Loaded {len(wordlist)} words from {file_path}")
            return True
        except Exception as e:
            self.print_error(f"Error loading wordlist: {str(e)}")
            return False
