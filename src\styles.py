"""
Styles for the IPTV Checker application.
This module contains stylesheets and color definitions for the application UI.
"""

# Main application style
MAIN_STYLE = """
QMainWindow {
    background-color: #2D3250;
    color: #F1F6F9;
}

QTabWidget::pane {
    border: 1px solid #424769;
    background-color: #2D3250;
    border-radius: 5px;
}

QTabBar::tab {
    background-color: #424769;
    color: #F1F6F9;
    padding: 8px 15px;
    margin-right: 2px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

QTabBar::tab:selected {
    background-color: #676F9D;
    color: #FFFFFF;
    font-weight: bold;
}

QTabBar::tab:hover:!selected {
    background-color: #535C91;
}

QWidget {
    background-color: #2D3250;
    color: #F1F6F9;
}

QLabel {
    color: #F1F6F9;
    font-weight: normal;
}

QGroupBox {
    border: 1px solid #424769;
    border-radius: 5px;
    margin-top: 10px;
    font-weight: bold;
    color: #F1F6F9;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 5px;
}

QPushButton {
    background-color: #676F9D;
    color: #FFFFFF;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #7A84B2;
}

QPushButton:pressed {
    background-color: #535C91;
}

QPushButton:disabled {
    background-color: #424769;
    color: #9DA5C9;
}

QLineEdit, QTextEdit, QSpinBox, QComboBox {
    background-color: #424769;
    color: #F1F6F9;
    border: 1px solid #676F9D;
    border-radius: 5px;
    padding: 5px;
}

QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QComboBox:focus {
    border: 1px solid #7A84B2;
}

QTableWidget {
    background-color: #424769;
    color: #F1F6F9;
    gridline-color: #676F9D;
    border: 1px solid #676F9D;
    border-radius: 5px;
}

QHeaderView::section {
    background-color: #535C91;
    color: #FFFFFF;
    padding: 5px;
    border: 1px solid #676F9D;
    font-weight: bold;
}

QTableWidget::item:selected {
    background-color: #676F9D;
    color: #FFFFFF;
}

QProgressBar {
    border: 1px solid #676F9D;
    border-radius: 5px;
    text-align: center;
    background-color: #424769;
    color: #FFFFFF;
}

QProgressBar::chunk {
    background-color: #535C91;
    width: 10px;
    margin: 0.5px;
}

QCheckBox {
    color: #F1F6F9;
}

QCheckBox::indicator {
    width: 15px;
    height: 15px;
}

QCheckBox::indicator:unchecked {
    border: 1px solid #676F9D;
    background-color: #424769;
}

QCheckBox::indicator:checked {
    border: 1px solid #676F9D;
    background-color: #535C91;
}

QRadioButton {
    color: #F1F6F9;
}

QRadioButton::indicator {
    width: 15px;
    height: 15px;
}

QRadioButton::indicator:unchecked {
    border: 1px solid #676F9D;
    border-radius: 7px;
    background-color: #424769;
}

QRadioButton::indicator:checked {
    border: 1px solid #676F9D;
    border-radius: 7px;
    background-color: #535C91;
}

QStatusBar {
    background-color: #424769;
    color: #F1F6F9;
}

QMenuBar {
    background-color: #424769;
    color: #F1F6F9;
}

QMenuBar::item {
    background-color: transparent;
}

QMenuBar::item:selected {
    background-color: #535C91;
}

QMenu {
    background-color: #424769;
    color: #F1F6F9;
    border: 1px solid #676F9D;
}

QMenu::item:selected {
    background-color: #535C91;
}
"""

# Color definitions for different statuses
COLORS = {
    "success": "#C8FFC8",  # Light green
    "error": "#FFC8C8",    # Light red
    "warning": "#FFFFC8",  # Light yellow
    "info": "#C8C8FF",     # Light blue
    "dark_success": "#28A745",  # Dark green
    "dark_error": "#DC3545",    # Dark red
    "dark_warning": "#FFC107",  # Dark yellow
    "dark_info": "#17A2B8",     # Dark blue
}

# Function to get color for a specific status
def get_status_color(status):
    """
    Get color for a specific status with better contrast.

    Args:
        status: Status string

    Returns:
        QColor object
    """
    from PyQt5.QtGui import QColor

    if 'Active' in status or 'Success' in status or 'Valid' in status:
        return QColor("#4CAF50")  # Dark green for better contrast
    elif 'Error' in status or 'Failed' in status:
        return QColor("#F44336")  # Dark red for better contrast
    elif 'Inactive' in status or 'Warning' in status:
        return QColor("#FF9800")  # Dark orange for better contrast
    else:
        return QColor("#2196F3")  # Dark blue for better contrast

# Function to get text color for a specific background
def get_text_color_for_background(background_color):
    """
    Get appropriate text color for a given background color.

    Args:
        background_color: QColor object

    Returns:
        QColor object for text
    """
    from PyQt5.QtGui import QColor

    # Calculate luminance to determine if we need light or dark text
    r, g, b = background_color.red(), background_color.green(), background_color.blue()
    luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255

    # If background is light, use dark text; if dark, use light text
    if luminance > 0.5:
        return QColor("#000000")  # Black text for light backgrounds
    else:
        return QColor("#FFFFFF")  # White text for dark backgrounds
