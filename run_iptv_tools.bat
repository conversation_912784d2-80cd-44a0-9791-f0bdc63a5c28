@echo off
REM Run script for IPTV Tools on Windows

echo Starting IPTV Tools...

REM Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python is not installed or not in PATH. Please install Python to run this application.
    pause
    exit /b 1
)

REM Check if required packages are installed
python -c "import PyQt5" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Installing required packages...
    pip install PyQt5 requests python-telegram-bot
)

REM Run the application
python src/app.py

pause
