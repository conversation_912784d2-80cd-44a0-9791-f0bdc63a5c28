"""
Statistics tracking for the IPTV Checker application.
This module contains functionality for tracking and reporting statistics.
"""

import time
import json
import os
import logging
from datetime import datetime

# Logger
logger = logging.getLogger("iptv_checker.stats")

class StatsTracker:
    """
    Class for tracking statistics during checking operations.
    """
    
    def __init__(self, checker_type):
        """
        Initialize a new stats tracker.
        
        Args:
            checker_type (str): Type of checker (e.g., 'm3u', 'xtream', 'mac')
        """
        self.checker_type = checker_type
        self.start_time = None
        self.end_time = None
        self.total_checked = 0
        self.total_valid = 0
        self.total_errors = 0
        self.error_types = {}
        self.check_rate = 0  # Items per second
        
        # Load previous stats if available
        self.history = self._load_history()
    
    def start(self):
        """Start tracking statistics."""
        self.start_time = time.time()
        self.total_checked = 0
        self.total_valid = 0
        self.total_errors = 0
        self.error_types = {}
        logger.info(f"Started stats tracking for {self.checker_type}")
    
    def stop(self):
        """Stop tracking statistics and save results."""
        self.end_time = time.time()
        duration = self.end_time - self.start_time if self.start_time else 0
        
        if duration > 0:
            self.check_rate = self.total_checked / duration
        
        # Save to history
        if self.start_time:
            self._save_session()
        
        logger.info(f"Stopped stats tracking for {self.checker_type}")
        return self.get_summary()
    
    def record_result(self, result):
        """
        Record a check result.
        
        Args:
            result (dict): Result dictionary from checker
        """
        self.total_checked += 1
        
        # Track valid results
        if result.get('is_valid', False):
            self.total_valid += 1
        
        # Track errors
        status = result.get('status', '')
        if 'Error' in status or 'Timeout' in status or 'Connection Error' in status:
            self.total_errors += 1
            
            # Count error types
            error_type = status.split(':')[0] if ':' in status else status
            self.error_types[error_type] = self.error_types.get(error_type, 0) + 1
    
    def get_summary(self):
        """
        Get a summary of the current statistics.
        
        Returns:
            dict: Statistics summary
        """
        duration = (self.end_time or time.time()) - self.start_time if self.start_time else 0
        
        return {
            'checker_type': self.checker_type,
            'start_time': datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S') if self.start_time else None,
            'duration': duration,
            'duration_formatted': self._format_duration(duration),
            'total_checked': self.total_checked,
            'total_valid': self.total_valid,
            'success_rate': (self.total_valid / self.total_checked * 100) if self.total_checked > 0 else 0,
            'total_errors': self.total_errors,
            'error_rate': (self.total_errors / self.total_checked * 100) if self.total_checked > 0 else 0,
            'error_types': self.error_types,
            'check_rate': self.check_rate,
            'estimated_time_remaining': self._estimate_time_remaining()
        }
    
    def _format_duration(self, seconds):
        """
        Format duration in seconds to human-readable format.
        
        Args:
            seconds (float): Duration in seconds
            
        Returns:
            str: Formatted duration
        """
        minutes, seconds = divmod(int(seconds), 60)
        hours, minutes = divmod(minutes, 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"
    
    def _estimate_time_remaining(self, total=None):
        """
        Estimate time remaining based on current progress.
        
        Args:
            total (int, optional): Total items to check
            
        Returns:
            str: Estimated time remaining
        """
        if not self.start_time or self.total_checked == 0 or not total:
            return "Unknown"
        
        elapsed = time.time() - self.start_time
        rate = self.total_checked / elapsed
        
        if rate == 0:
            return "Unknown"
        
        remaining_items = total - self.total_checked
        remaining_seconds = remaining_items / rate
        
        return self._format_duration(remaining_seconds)
    
    def _load_history(self):
        """
        Load statistics history from file.
        
        Returns:
            list: History of statistics sessions
        """
        history_file = f"stats_{self.checker_type}.json"
        
        if os.path.exists(history_file):
            try:
                with open(history_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading stats history: {str(e)}")
        
        return []
    
    def _save_session(self):
        """Save current session to history file."""
        history_file = f"stats_{self.checker_type}.json"
        
        # Add current session to history
        session = self.get_summary()
        session['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        self.history.append(session)
        
        # Keep only last 100 sessions
        if len(self.history) > 100:
            self.history = self.history[-100:]
        
        # Save to file
        try:
            with open(history_file, 'w') as f:
                json.dump(self.history, f, indent=4)
        except Exception as e:
            logger.error(f"Error saving stats history: {str(e)}")
    
    def get_history(self):
        """
        Get statistics history.
        
        Returns:
            list: History of statistics sessions
        """
        return self.history
