# متطلبات تطبيق IPTV Tools

## الوظائف الرئيسية
1. **MAC Scanner**: أداة لفحص عناوين MAC وتحديد ما إذا كانت نشطة
2. **User-Pass Checker**: أداة للتحقق من صحة بيانات اعتماد المستخدم (اسم المستخدم وكلمة المرور)
3. **M3U Checker**: أداة للتحقق من صحة وعمل روابط M3U
4. **Stalker Player**: مشغل للمحتوى
5. **IPTV Player**: مشغل لقنوات IPTV
6. **Tools**: أدوات إضافية متنوعة
7. **حفظ النتائج**: حفظ النتائج الجيدة في ملف خارجي بشكل تلقائي أثناء الفحص
8. **تكامل مع Telegram**: إرسال النتائج إلى بوت تيليجرام

## متطلبات الواجهة
1. واجهة مستخدم رسومية مشابهة للتطبيق الأصلي
2. تبويبات متعددة للوظائف المختلفة
3. عرض النتائج في جدول مع إمكانية التصدير بتنسيقات مختلفة (CSV, XLS, TXT)
4. إمكانية تحديث وتحديد النتائج

## متطلبات الأداء
1. دعم الفحص المتزامن (متعدد الخيوط)
2. إمكانية تحديد عدد الخيوط وفترات الانتظار
3. عرض تقدم العملية وإحصائيات الفحص

## متطلبات دعم البروكسي
1. دعم استخدام البروكسي للفحص
2. قراءة معلومات البروكسي من ملف خارجي (proxy.txt)
3. دعم تنسيقات مختلفة للبروكسي مثل:
   - `(Http)proxy.uem.br:8080:pg54370:bfe3fd17`
   - `(Socks5)yul.socks.privado.io:1080:enaqcjt61537:1l4lcv1x8otj`

## متطلبات حفظ النتائج
1. حفظ النتائج الجيدة تلقائياً أثناء عملية الفحص
2. دعم تصدير النتائج بتنسيقات متعددة (CSV, XLS, TXT)
3. تخزين سجل النتائج السابقة

## متطلبات تكامل Telegram
1. إضافة خانة لإدخال معلومات بوت Telegram (Token و Chat ID)
2. إرسال النتائج الجيدة تلقائياً إلى بوت Telegram
3. إمكانية تخصيص تنسيق الرسائل المرسلة

## متطلبات النظام
1. التوافق مع نظام التشغيل Windows
2. واجهة مستخدم سهلة الاستخدام
3. إمكانية التحديث المستقبلي
