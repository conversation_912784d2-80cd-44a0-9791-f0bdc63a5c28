import requests
import re
import threading
import time
import datetime
import socket
import json
from PyQt5.QtCore import QObject, pyqtSignal

class PortalExtractor(QObject):
    """Class for handling Stalker Portal extraction functionality"""

    # Signals for updating UI
    result_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)  # current, total
    status_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False
        self.threads = []
        self.max_threads = 10
        self.timeout = 5
        self.results = []
        self.total_count = 0
        self.processed_count = 0

    def extract_from_file(self, file_path):
        """Extract Stalker portals from a file with URLs"""
        self.status_signal.emit(f"Extracting Stalker portals from file: {file_path}")
        portals = []

        try:
            # First try line-by-line extraction (for simple URL lists)
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

                # Check if it's a text file with URLs or a more complex file
                if len(content.strip().splitlines()) < 1000:  # Simple URL list
                    for line in content.splitlines():
                        line = line.strip()
                        if line:
                            # Extract URL part
                            if line.startswith('http://') or line.startswith('https://'):
                                url = line
                            else:
                                # Add http:// if missing
                                url = f"http://{line}"

                            # Clean up URL
                            url = self.clean_portal_url(url)
                            if url and url not in portals and self.is_potential_stalker_portal(url):
                                portals.append(url)
                                self.result_signal.emit({"url": url, "source": "file"})

                # If we didn't find enough URLs or it's a more complex file, try pattern matching
                if len(portals) < 5:
                    # First try to find URLs matching specific Stalker patterns
                    stalker_patterns = [
                        r'https?://[^/\s"\'<>]+/c/',
                        r'https?://[^/\s"\'<>]+/stalker_portal/',
                        r'https?://[^/\s"\'<>]+/ministra/',
                        r'https?://[^/\s"\'<>]+/portal/',
                        r'https?://[^/\s"\'<>]+/stb/',
                        r'https?://[^/\s"\'<>]+/player/'
                    ]

                    for pattern in stalker_patterns:
                        stalker_urls = re.findall(pattern, content)
                        for url in stalker_urls:
                            clean_url = self.clean_portal_url(url)
                            if clean_url and clean_url not in portals and self.is_potential_stalker_portal(clean_url):
                                portals.append(clean_url)
                                self.result_signal.emit({"url": clean_url, "source": "file (pattern)"})

                    # If still not enough, try general URL extraction
                    if len(portals) < 10:
                        # Extract all URLs
                        urls = re.findall(r'https?://[^\s"\'<>]+', content)

                        for url in urls:
                            # Clean up URL
                            clean_url = self.clean_portal_url(url)
                            # Only add if it's a potential Stalker portal and not already in the list
                            if clean_url and clean_url not in portals and self.is_likely_stalker_portal(clean_url):
                                portals.append(clean_url)
                                self.result_signal.emit({"url": clean_url, "source": "file (URL)"})

                    # Look for IP addresses with ports that might be Stalker portals
                    ip_port_pattern = r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{1,5})'
                    ip_port_matches = re.findall(ip_port_pattern, content)

                    for ip, port in ip_port_matches:
                        # Check if IP is valid
                        if all(0 <= int(octet) <= 255 for octet in ip.split('.')):
                            # Check if port is valid
                            if 1 <= int(port) <= 65535:
                                # Construct potential Stalker portal URLs
                                potential_urls = [
                                    f"http://{ip}:{port}/c/",
                                    f"http://{ip}:{port}/stalker_portal/",
                                    f"http://{ip}:{port}/ministra/",
                                    f"http://{ip}:{port}/portal/",
                                    f"http://{ip}:{port}/stb/"
                                ]

                                for potential_url in potential_urls:
                                    if potential_url not in portals:
                                        portals.append(potential_url)
                                        self.result_signal.emit({"url": potential_url, "source": "file (IP:port)"})

            # Filter out any remaining non-Stalker URLs
            filtered_portals = []
            for url in portals:
                if self.is_likely_stalker_portal(url):
                    filtered_portals.append(url)

            return filtered_portals
        except Exception as e:
            print(f"Error extracting Stalker portals from file: {e}")
            return []

    def clean_portal_url(self, url):
        """Clean and format Stalker portal URL"""
        try:
            # Add http:// if missing
            if not url.startswith('http://') and not url.startswith('https://'):
                url = f"http://{url}"

            # Remove trailing paths except /c/ or /stalker_portal/
            if '/c/' not in url and '/stalker_portal/' not in url:
                url = re.sub(r'(/[^/]*)?$', '', url)
            elif '/c/' in url:
                url = re.sub(r'(/[^/]*)?$', '/c/', url)
            elif '/stalker_portal/' in url:
                url = re.sub(r'(/[^/]*)?$', '/stalker_portal/', url)

            return url
        except:
            return url

    def is_potential_stalker_portal(self, url):
        """Check if URL is potentially a Stalker portal (initial filter)"""
        url_lower = url.lower()

        # Exclude common non-Stalker sites
        excluded_domains = [
            'youtube.com',
            'wikimedia.org',
            'wikipedia.org',
            'facebook.com',
            'twitter.com',
            'instagram.com',
            'github.com',
            'cdn.',
            'googleusercontent',
            'amazonaws.com',
            'cloudfront.net',
            'akamaized.net',
            'google.com',
            'bing.com',
            'yahoo.com',
            'baidu.com',
            'yandex.com',
            'reddit.com',
            'imgur.com',
            'pinterest.com',
            'wordpress.com',
            'blogspot.com',
            'tumblr.com',
            'medium.com'
        ]

        # If URL contains any excluded domain, it's not a Stalker portal
        if any(domain in url_lower for domain in excluded_domains):
            return False

        # Check for Stalker portal indicators in URL
        stalker_indicators = [
            '/c/',
            '/stalker_portal/',
            '/portal/',
            '/ministra/',
            '/stb/',
            '/player/',
            'ministra',
            'stalker',
            'middleware',
            'infomir',
            'mag',
            'iptv/c'
        ]

        # If URL contains any of the indicators, consider it a potential Stalker portal
        return any(indicator in url_lower for indicator in stalker_indicators)

    def is_likely_stalker_portal(self, url):
        """More strict check if URL is likely a Stalker portal (secondary filter)"""
        url_lower = url.lower()

        # Strong indicators that this is definitely a Stalker portal
        strong_indicators = [
            '/stalker_portal/',
            '/c/portal.php',
            '/c/server/load.php',
            '/ministra/portal.php',
            '/ministra/server/load.php',
            '/stb/portal.php',
            '/portal/stb/',
            '/portal/server/load.php'
        ]

        # If URL contains any strong indicator, it's very likely a Stalker portal
        if any(indicator in url_lower for indicator in strong_indicators):
            return True

        # Check for common Stalker portal URL patterns
        stalker_patterns = [
            # Pattern: domain/c/ or domain/stalker_portal/
            r'^https?://[^/]+/(?:c|stalker_portal)/?$',
            # Pattern: domain/ministra/
            r'^https?://[^/]+/ministra/?$',
            # Pattern: domain/stb/
            r'^https?://[^/]+/stb/?$',
            # Pattern: domain/portal/
            r'^https?://[^/]+/portal/?$'
        ]

        # Check if URL matches any of the patterns
        for pattern in stalker_patterns:
            if re.match(pattern, url_lower):
                return True

        # If we got here, apply more filtering
        # Exclude URLs with common file extensions that are definitely not portals
        file_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.mp4', '.mp3', '.avi', '.mov', '.zip', '.rar']
        if any(ext in url_lower for ext in file_extensions):
            return False

        # Default to the initial check result
        return self.is_potential_stalker_portal(url)

    def is_enigma2_portal(self, url):
        """Check if URL is an Enigma2 portal"""
        url_lower = url.lower()

        # Enigma2 portal indicators
        enigma2_indicators = [
            '/web/',
            '/web/stream',
            '/web/services',
            '/web/epgservice',
            '/web/movielist',
            '/web/getservices',
            '/web/getallservices',
            '/web/getcurrent',
            '/web/subservices',
            '/web/epgservice',
            '/web/epgservicenow',
            '/web/epgnow',
            '/web/epgnext',
            'enigma2',
            'dreambox',
            'openwebif',
            'vuplus'
        ]

        # If URL contains any of the indicators, consider it a potential Enigma2 portal
        return any(indicator in url_lower for indicator in enigma2_indicators)

    def is_tvheadend_portal(self, url):
        """Check if URL is a TVHeadend portal"""
        url_lower = url.lower()

        # TVHeadend portal indicators
        tvheadend_indicators = [
            '/tvheadend/',
            '/hts/',
            '/htsp/',
            '/play/',
            '/stream/channel/',
            '/xmltv/',
            '/epg/',
            '/api/epg/',
            '/api/channel/',
            'tvheadend',
            'htsapp'
        ]

        # If URL contains any of the indicators, consider it a potential TVHeadend portal
        return any(indicator in url_lower for indicator in tvheadend_indicators)

    def is_xtream_codes_portal(self, url):
        """Check if URL is an Xtream-Codes portal"""
        url_lower = url.lower()

        # Xtream-Codes portal indicators
        xtream_indicators = [
            '/player_api.php',
            '/xmltv.php',
            '/panel_api.php',
            '/live/',
            '/movie/',
            '/series/',
            '/get.php',
            '/panel/',
            'xtream',
            'iptv_panel',
            'iptvpanel'
        ]

        # If URL contains any of the indicators, consider it a potential Xtream-Codes portal
        return any(indicator in url_lower for indicator in xtream_indicators)

    def verify_xtream_portal(self, url):
        """Verify if URL is a valid Xtream-Codes portal and extract information"""
        try:
            # Headers for requests
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }

            # Initialize portal info dictionary
            portal_info = {
                "url": url,
                "is_portal": False,
                "portal_type": "Xtream-Codes",
                "version": "Unknown",
                "channels_count": 0,
                "response_time": 0,
                "country": "Unknown",
                "last_checked": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # Test endpoints
            endpoints = [
                "/player_api.php",
                "/panel_api.php",
                "/xmltv.php",
                "/get.php"
            ]

            for endpoint in endpoints:
                # Construct endpoint URL
                base_url = url.rstrip("/")
                check_url = f"{base_url}{endpoint}"

                # Measure response time
                start_time = time.time()
                response = requests.get(check_url, headers=headers, timeout=self.timeout)
                end_time = time.time()
                response_time = round((end_time - start_time) * 1000)  # in milliseconds
                portal_info["response_time"] = response_time

                # Check if response indicates an Xtream-Codes portal
                if response.status_code == 200:
                    try:
                        # Try to parse as JSON
                        data = response.json()

                        # Check for Xtream-specific fields
                        if "user_info" in data or "categories" in data or "available_channels" in data:
                            portal_info["is_portal"] = True

                            # Try to extract version
                            if "server_info" in data and "version" in data["server_info"]:
                                portal_info["version"] = data["server_info"]["version"]

                            # Try to extract channels count
                            if "categories" in data:
                                # Count channels in all categories
                                channels_count = 0
                                for category in data["categories"]:
                                    if "channels" in category:
                                        channels_count += len(category["channels"])
                                portal_info["channels_count"] = channels_count

                            # Try to determine country from IP
                            try:
                                ip = re.search(r'https?://([^:/]+)', url).group(1)
                                if not ip.startswith('192.168.') and not ip.startswith('10.') and not ip.startswith('172.'):
                                    # Only lookup external IPs
                                    portal_info["country"] = self.get_country_from_ip(ip)
                            except:
                                pass

                            self.result_signal.emit(portal_info)
                            return True
                    except:
                        # Not a JSON response, check content
                        content = response.text.lower()

                        # Check for Xtream-Codes indicators in content
                        xtream_content_indicators = [
                            "xtream codes",
                            "iptv panel",
                            "player api",
                            "panel api",
                            "iptv management"
                        ]

                        if any(indicator in content for indicator in xtream_content_indicators):
                            portal_info["is_portal"] = True
                            self.result_signal.emit(portal_info)
                            return True

            return False
        except Exception as e:
            print(f"Error verifying Xtream-Codes portal {url}: {e}")
            return False

    def search_portals_online(self):
        """Search for latest Stalker portal lists online"""
        self.status_signal.emit("Searching for Stalker portals online...")
        portals = []

        # List of specialized sources for Stalker portal lists
        sources = [
            "https://raw.githubusercontent.com/iptv-org/iptv/master/sources.json",
            "https://raw.githubusercontent.com/Free-TV/IPTV/master/playlist.m3u8",
            "https://raw.githubusercontent.com/iptv-org/database/master/data/providers.json",
            "https://github.com/StalkerPortals/stalker-portals-list/raw/main/portals.txt",
            "https://github.com/StalkerPortals/stalker-portals/raw/main/portals.txt"
        ]

        # Known Stalker portal patterns to look for in content
        stalker_patterns = [
            r'https?://[^/\s"\'<>]+/c/',
            r'https?://[^/\s"\'<>]+/stalker_portal/',
            r'https?://[^/\s"\'<>]+/ministra/',
            r'https?://[^/\s"\'<>]+/portal/',
            r'https?://[^/\s"\'<>]+/stb/',
            r'https?://[^/\s"\'<>]+/player/'
        ]

        for source in sources:
            try:
                response = requests.get(source, timeout=10)
                if response.status_code == 200:
                    content = response.text

                    # First try to find URLs matching specific Stalker patterns
                    for pattern in stalker_patterns:
                        stalker_urls = re.findall(pattern, content)
                        for url in stalker_urls:
                            clean_url = self.clean_portal_url(url)
                            if clean_url and clean_url not in portals and self.is_potential_stalker_portal(clean_url):
                                portals.append(clean_url)
                                self.result_signal.emit({"url": clean_url, "source": "online"})

                    # Then try general URL extraction as fallback
                    if len(portals) < 5:  # Only if we didn't find enough specific matches
                        urls = re.findall(r'https?://[^\s"\'<>]+', content)
                        for url in urls:
                            clean_url = self.clean_portal_url(url)
                            if clean_url and clean_url not in portals and self.is_potential_stalker_portal(clean_url):
                                portals.append(clean_url)
                                self.result_signal.emit({"url": clean_url, "source": "online"})
            except Exception as e:
                print(f"Error searching Stalker portals from {source}: {e}")

        # Try to find Stalker portal lists from search engines
        if len(portals) < 10:  # Only if we need more portals
            try:
                search_terms = [
                    "stalker portal list",
                    "ministra portal list",
                    "stalker player portal",
                    "stalker middleware portal",
                    "stalker iptv portal",
                    "infomir mag portal",
                    "stalker portal server address"
                ]

                for term in search_terms:
                    search_url = f"https://www.google.com/search?q={term.replace(' ', '+')}"
                    headers = {
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                    }

                    response = requests.get(search_url, headers=headers, timeout=10)
                    if response.status_code == 200:
                        content = response.text

                        # First try specific Stalker patterns
                        for pattern in stalker_patterns:
                            stalker_urls = re.findall(pattern, content)
                            for url in stalker_urls:
                                clean_url = self.clean_portal_url(url)
                                if clean_url and clean_url not in portals and self.is_potential_stalker_portal(clean_url):
                                    portals.append(clean_url)
                                    self.result_signal.emit({"url": clean_url, "source": "search"})
            except Exception as e:
                print(f"Error searching Stalker portals from search engines: {e}")

        # Filter out any remaining non-Stalker URLs
        filtered_portals = []
        for url in portals:
            if self.is_likely_stalker_portal(url):
                filtered_portals.append(url)

        return filtered_portals

    def verify_portal(self, url):
        """Verify if URL is a valid portal of any supported type and extract additional information"""
        try:
            # First, check what type of portal this might be
            portal_type = "Unknown"

            if self.is_likely_stalker_portal(url):
                portal_type = "Stalker"
            elif self.is_xtream_codes_portal(url):
                portal_type = "Xtream-Codes"
            elif self.is_enigma2_portal(url):
                portal_type = "Enigma2"
            elif self.is_tvheadend_portal(url):
                portal_type = "TVHeadend"
            else:
                # Not a recognized portal type
                return False

            # Use the appropriate verification method based on portal type
            if portal_type == "Xtream-Codes":
                return self.verify_xtream_portal(url)
            elif portal_type == "Enigma2":
                return self.verify_enigma2_portal(url)
            elif portal_type == "TVHeadend":
                return self.verify_tvheadend_portal(url)
            else:
                # Default to Stalker portal verification
                return self.verify_stalker_portal(url)
        except Exception as e:
            print(f"Error verifying portal {url}: {e}")
            return False

    def verify_stalker_portal(self, url):
        """Verify if URL is a valid Stalker portal and extract additional information"""
        try:
            # Headers for requests
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }

            # Initialize portal info dictionary
            portal_info = {
                "url": url,
                "is_portal": False,
                "portal_type": "Stalker",
                "version": "Unknown",
                "channels_count": 0,
                "response_time": 0,
                "country": "Unknown",
                "last_checked": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # Check common Stalker portal endpoints
            endpoints = [
                "",  # Base URL
                "/c/",
                "/c/portal.php",
                "/c/server/load.php",
                "/stalker_portal/",
                "/stalker_portal/c/",
                "/stalker_portal/server/load.php",
                "/portal/",
                "/portal.php",
                "/portal/server/load.php",
                "/ministra/",
                "/ministra/portal.php",
                "/ministra/server/load.php",
                "/stb/",
                "/stb/portal.php",
                "/player/",
                "/server/load.php",
                "/client_area/index.php",
                "/api/stb/handshake"
            ]

            for endpoint in endpoints:
                # Construct endpoint URL properly
                base_url = url.rstrip("/")
                check_url = f"{base_url}{endpoint}"

                try:
                    # Measure response time
                    start_time = time.time()
                    response = requests.get(check_url, headers=headers, timeout=self.timeout)
                    end_time = time.time()
                    response_time = round((end_time - start_time) * 1000)  # in milliseconds
                    portal_info["response_time"] = response_time

                    # Check if response indicates a Stalker portal
                    if response.status_code == 200:
                        # Try to determine country from IP
                        try:
                            ip = re.search(r'https?://([^:/]+)', url).group(1)
                            if not ip.startswith('192.168.') and not ip.startswith('10.') and not ip.startswith('172.'):
                                # Only lookup external IPs
                                portal_info["country"] = self.get_country_from_ip(ip)
                        except:
                            pass

                        # Check for JSON response with Stalker-specific fields
                        if "application/json" in response.headers.get("Content-Type", ""):
                            try:
                                json_data = response.json()
                                # Check for Stalker-specific JSON fields
                                stalker_json_fields = ["js", "token", "mac", "stb_type", "portal", "authorized"]
                                if any(field in json_data for field in stalker_json_fields):
                                    portal_info["is_portal"] = True

                                    # Try to extract version
                                    if "js" in json_data and "version" in json_data["js"]:
                                        portal_info["version"] = json_data["js"]["version"]

                                    # Try to extract channels count if available
                                    if "channels_count" in json_data:
                                        portal_info["channels_count"] = json_data["channels_count"]
                                    else:
                                        # Try to get channels count
                                        portal_info["channels_count"] = self.get_channels_count(url, "Stalker")

                                    self.result_signal.emit(portal_info)
                                    return True
                            except:
                                pass

                        # Check content for Stalker indicators
                        content = response.text.lower()

                        # Determine more specific portal type
                        if "ministra" in content:
                            portal_info["portal_type"] = "Ministra"
                        elif "stalker" in content:
                            portal_info["portal_type"] = "Stalker"
                        elif "middleware" in content:
                            portal_info["portal_type"] = "Middleware"
                        elif "infomir" in content:
                            portal_info["portal_type"] = "Infomir"

                        # Try to extract version
                        version_patterns = [
                            r'version["\s:=]+([0-9.]+)',
                            r'v\.([0-9.]+)',
                            r'ver["\s:=]+([0-9.]+)'
                        ]

                        for pattern in version_patterns:
                            version_match = re.search(pattern, content)
                            if version_match:
                                portal_info["version"] = version_match.group(1)
                                break

                        # Strong indicators in response content
                        strong_indicators = [
                            "stalker portal",
                            "ministra portal",
                            "middleware server",
                            "stb device",
                            "mag device",
                            "infomir",
                            "stalker middleware",
                            "iptv middleware",
                            "stalker api",
                            "stb api"
                        ]

                        # Check for strong indicators first
                        if any(indicator in content for indicator in strong_indicators):
                            portal_info["is_portal"] = True
                            # Try to get channels count
                            portal_info["channels_count"] = self.get_channels_count(url, portal_info["portal_type"])
                            self.result_signal.emit(portal_info)
                            return True

                        # Check for specific Stalker HTML elements or scripts
                        html_indicators = [
                            "stb.init",
                            "stalker_portal",
                            "ministra_portal",
                            "stbPlayer",
                            "mag_player",
                            "stalkerLoader",
                            "portalManager",
                            "stb.SetDefaultParams"
                        ]

                        if any(indicator in content for indicator in html_indicators):
                            portal_info["is_portal"] = True
                            # Try to get channels count
                            portal_info["channels_count"] = self.get_channels_count(url, portal_info["portal_type"])
                            self.result_signal.emit(portal_info)
                            return True

                        # Check for general Stalker portal indicators as fallback
                        general_indicators = [
                            "stalker",
                            "ministra",
                            "middleware",
                            "portal",
                            "stb",
                            "mag",
                            "infomir"
                        ]

                        if any(indicator in content for indicator in general_indicators):
                            # Additional check to avoid false positives
                            # Make sure it's not just a random page mentioning these terms
                            if len(content) < 10000 and not any(term in content for term in ["youtube", "wikipedia", "facebook", "twitter"]):
                                portal_info["is_portal"] = True
                                # Try to get channels count
                                portal_info["channels_count"] = self.get_channels_count(url, portal_info["portal_type"])
                                self.result_signal.emit(portal_info)
                                return True
                except:
                    pass

            return False
        except Exception as e:
            print(f"Error verifying Stalker portal {url}: {e}")
            return False

    def verify_enigma2_portal(self, url):
        """Verify if URL is a valid Enigma2 portal and extract information"""
        try:
            # Headers for requests
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }

            # Initialize portal info dictionary
            portal_info = {
                "url": url,
                "is_portal": False,
                "portal_type": "Enigma2",
                "version": "Unknown",
                "channels_count": 0,
                "response_time": 0,
                "country": "Unknown",
                "last_checked": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # Test endpoints
            endpoints = [
                "/web/",
                "/web/getservices",
                "/web/getallservices",
                "/web/getcurrent",
                "/web/epgnow",
                "/web/epgnext"
            ]

            for endpoint in endpoints:
                # Construct endpoint URL
                base_url = url.rstrip("/")
                check_url = f"{base_url}{endpoint}"

                # Measure response time
                start_time = time.time()
                response = requests.get(check_url, headers=headers, timeout=self.timeout)
                end_time = time.time()
                response_time = round((end_time - start_time) * 1000)  # in milliseconds
                portal_info["response_time"] = response_time

                # Check if response indicates an Enigma2 portal
                if response.status_code == 200:
                    # Try to determine country from IP
                    try:
                        ip = re.search(r'https?://([^:/]+)', url).group(1)
                        if not ip.startswith('192.168.') and not ip.startswith('10.') and not ip.startswith('172.'):
                            # Only lookup external IPs
                            portal_info["country"] = self.get_country_from_ip(ip)
                    except:
                        pass

                    # Check for XML response with Enigma2-specific content
                    content = response.text.lower()

                    # Check for Enigma2 indicators in content
                    enigma2_indicators = [
                        "<e2servicereference>",
                        "<e2servicename>",
                        "<e2service>",
                        "<e2event>",
                        "<e2eventservicereference>",
                        "<e2eventservicename>",
                        "enigma2",
                        "dreambox",
                        "openwebif",
                        "vuplus"
                    ]

                    if any(indicator in content for indicator in enigma2_indicators):
                        portal_info["is_portal"] = True

                        # Try to extract version
                        version_match = re.search(r'<e2webifversion>([^<]+)</e2webifversion>', content)
                        if version_match:
                            portal_info["version"] = version_match.group(1)

                        # Try to count channels
                        if "<e2servicereference>" in content:
                            portal_info["channels_count"] = content.count("<e2servicereference>")

                        self.result_signal.emit(portal_info)
                        return True

            return False
        except Exception as e:
            print(f"Error verifying Enigma2 portal {url}: {e}")
            return False

    def verify_tvheadend_portal(self, url):
        """Verify if URL is a valid TVHeadend portal and extract information"""
        try:
            # Headers for requests
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }

            # Initialize portal info dictionary
            portal_info = {
                "url": url,
                "is_portal": False,
                "portal_type": "TVHeadend",
                "version": "Unknown",
                "channels_count": 0,
                "response_time": 0,
                "country": "Unknown",
                "last_checked": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # Test endpoints
            endpoints = [
                "/api/channel/grid",
                "/api/epg/events/grid",
                "/api/status/connections",
                "/api/serverinfo",
                "/play/",
                "/xmltv/"
            ]

            for endpoint in endpoints:
                # Construct endpoint URL
                base_url = url.rstrip("/")
                check_url = f"{base_url}{endpoint}"

                # Measure response time
                start_time = time.time()
                response = requests.get(check_url, headers=headers, timeout=self.timeout)
                end_time = time.time()
                response_time = round((end_time - start_time) * 1000)  # in milliseconds
                portal_info["response_time"] = response_time

                # Check if response indicates a TVHeadend portal
                if response.status_code == 200:
                    # Try to determine country from IP
                    try:
                        ip = re.search(r'https?://([^:/]+)', url).group(1)
                        if not ip.startswith('192.168.') and not ip.startswith('10.') and not ip.startswith('172.'):
                            # Only lookup external IPs
                            portal_info["country"] = self.get_country_from_ip(ip)
                    except:
                        pass

                    # Check for JSON response with TVHeadend-specific fields
                    try:
                        data = response.json()

                        # Check for TVHeadend-specific fields
                        if "entries" in data or "totalCount" in data or "api" in data:
                            portal_info["is_portal"] = True

                            # Try to extract version
                            if "api" in data and "version" in data["api"]:
                                portal_info["version"] = data["api"]["version"]

                            # Try to extract channels count
                            if "entries" in data and endpoint == "/api/channel/grid":
                                portal_info["channels_count"] = len(data["entries"])

                            self.result_signal.emit(portal_info)
                            return True
                    except:
                        # Not a JSON response, check content
                        content = response.text.lower()

                        # Check for TVHeadend indicators in content
                        tvheadend_indicators = [
                            "tvheadend",
                            "htsp",
                            "hts",
                            "dvb",
                            "iptv",
                            "channel",
                            "epg",
                            "xmltv"
                        ]

                        if any(indicator in content for indicator in tvheadend_indicators):
                            portal_info["is_portal"] = True
                            self.result_signal.emit(portal_info)
                            return True

            return False
        except Exception as e:
            print(f"Error verifying TVHeadend portal {url}: {e}")
            return False

    def worker(self, urls):
        """Worker thread for verifying multiple URLs"""
        for url in urls:
            if not self.running:
                break

            # verify_portal now returns a boolean but emits the portal_info dictionary
            # with all the additional information via the result_signal
            is_portal = self.verify_portal(url)

            # Update progress regardless of result
            self.processed_count += 1
            self.progress_signal.emit(self.processed_count, self.total_count)

        # Thread completed
        return

    def start_verification(self, urls):
        """Start verifying URLs as portals"""
        if self.running:
            return False

        self.running = True
        self.processed_count = 0
        self.total_count = len(urls)
        self.results = []

        # Clear previous threads
        self.threads = []

        # Split work across threads
        chunk_size = max(1, len(urls) // self.max_threads)
        for i in range(0, len(urls), chunk_size):
            chunk = urls[i:i+chunk_size]
            thread = threading.Thread(
                target=self.worker,
                args=(chunk,)
            )
            thread.daemon = True
            self.threads.append(thread)
            thread.start()

        return True

    def stop_verification(self):
        """Stop the verification process"""
        self.running = False

        # Wait for all threads to complete
        for thread in self.threads:
            if thread.is_alive():
                thread.join(1.0)  # Wait with timeout

        self.status_signal.emit("Verification stopped")
        return True

    def get_country_from_ip(self, ip):
        """Get country from IP address using ip-api.com"""
        try:
            # Check if it's a valid IP address
            try:
                socket.inet_aton(ip)
            except socket.error:
                # If not an IP address, try to resolve domain to IP
                try:
                    ip = socket.gethostbyname(ip)
                except:
                    return "Unknown"

            # Skip private IP ranges
            if ip.startswith('192.168.') or ip.startswith('10.') or ip.startswith('172.16.'):
                return "Local"

            # Use ip-api.com for geolocation (free, no API key required)
            response = requests.get(f"http://ip-api.com/json/{ip}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    return data.get("country", "Unknown")
            return "Unknown"
        except Exception as e:
            print(f"Error getting country from IP {ip}: {e}")
            return "Unknown"

    def get_channels_count(self, url, portal_type):
        """Get number of channels available in the portal"""
        try:
            # Different endpoints based on portal type
            if portal_type == "Stalker" or portal_type == "Ministra":
                # Try to get channels list
                check_url = f"{url.rstrip('/')}/stalker_portal/api/channels"
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    "Authorization": "Bearer test" # Some portals require authorization
                }

                response = requests.get(check_url, headers=headers, timeout=self.timeout)
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if "channels" in data:
                            return len(data["channels"])
                    except:
                        pass

                # Alternative endpoint
                check_url = f"{url.rstrip('/')}/stalker_portal/api/itv/channels"
                response = requests.get(check_url, headers=headers, timeout=self.timeout)
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if "js" in data and "data" in data["js"]:
                            return len(data["js"]["data"])
                    except:
                        pass

            # For other portal types or if above methods fail
            return 0
        except Exception as e:
            print(f"Error getting channels count for {url}: {e}")
            return 0

    def export_to_m3u(self, portals, filename):
        """Export verified portals to M3U format"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                # Write M3U header
                f.write("#EXTM3U\n")

                # Write each portal as a channel
                for portal in portals:
                    portal_type = portal.get("portal_type", "Unknown")
                    country = portal.get("country", "Unknown")
                    version = portal.get("version", "Unknown")

                    # Create channel name with portal info
                    channel_name = f"{portal_type} Portal - {country} (v{version})"

                    # Write channel info
                    f.write(f'#EXTINF:-1 tvg-id="{portal["url"]}" tvg-name="{channel_name}" tvg-logo="" group-title="IPTV Portals",{channel_name}\n')
                    f.write(f'{portal["url"]}\n')

            return True
        except Exception as e:
            print(f"Error exporting to M3U: {e}")
            return False

    def export_to_json(self, portals, filename):
        """Export verified portals to JSON format"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(portals, f, indent=4)
            return True
        except Exception as e:
            print(f"Error exporting to JSON: {e}")
            return False
