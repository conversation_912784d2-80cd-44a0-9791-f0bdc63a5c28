import os
import threading
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QCheckBox, QSpinBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFileDialog, QProgressBar,
                            QGroupBox, QRadioButton, QMessageBox, QComboBox, QTabWidget)
from PyQt5.QtGui import QColor
from PyQt5.QtCore import Qt, pyqtSlot, QThread
from styles import get_status_color
from stream_analyzer import StreamAnalyzer
from format_converter import FormatConverter

class ToolsTab(QWidget):
    """Tab for various IPTV tools"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.stream_analyzer = None
        self.analysis_thread = None
        self.format_converter = None
        self.converter_thread = None
        self.init_ui()

    def init_ui(self):
        """Initialize UI components"""
        # Main layout
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)

        # Create tools tabs
        self.tools_tabs = QTabWidget()

        # Add tools tabs
        self.tools_tabs.addTab(self.create_converter_tab(), "Format Converter")
        self.tools_tabs.addTab(self.create_epg_tab(), "EPG Generator")
        self.tools_tabs.addTab(self.create_playlist_cleaner_tab(), "Playlist Cleaner")
        self.tools_tabs.addTab(self.create_playlist_merger_tab(), "Playlist Merger")
        self.tools_tabs.addTab(self.create_speed_test_tab(), "Server Speed Test")
        self.tools_tabs.addTab(self.create_stream_analyzer_tab(), "Stream Analyzer")

        main_layout.addWidget(self.tools_tabs)

    def create_converter_tab(self):
        """Create format converter tab"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # Input section
        input_group = QGroupBox("Input")
        input_layout = QVBoxLayout()

        # Input file
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("Input File:"))
        self.input_file = QLineEdit()
        file_layout.addWidget(self.input_file)
        browse_button = QPushButton("Browse")
        browse_button.clicked.connect(self.browse_input_file)
        file_layout.addWidget(browse_button)
        input_layout.addLayout(file_layout)

        # Input format
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("Input Format:"))
        self.input_format = QComboBox()
        self.input_format.addItems(["M3U", "M3U8", "XSPF", "XML", "JSON", "CSV"])
        self.input_format.currentIndexChanged.connect(self.on_input_format_changed)
        format_layout.addWidget(self.input_format)
        input_layout.addLayout(format_layout)

        input_group.setLayout(input_layout)
        layout.addWidget(input_group)

        # Output section
        output_group = QGroupBox("Output")
        output_layout = QVBoxLayout()

        # Output file
        out_file_layout = QHBoxLayout()
        out_file_layout.addWidget(QLabel("Output File:"))
        self.output_file = QLineEdit()
        out_file_layout.addWidget(self.output_file)
        out_browse_button = QPushButton("Browse")
        out_browse_button.clicked.connect(self.browse_output_file)
        out_file_layout.addWidget(out_browse_button)
        output_layout.addLayout(out_file_layout)

        # Output format
        out_format_layout = QHBoxLayout()
        out_format_layout.addWidget(QLabel("Output Format:"))
        self.output_format = QComboBox()
        self.output_format.addItems(["M3U", "M3U8", "XSPF", "XML", "JSON", "CSV"])
        self.output_format.currentIndexChanged.connect(self.on_output_format_changed)
        out_format_layout.addWidget(self.output_format)
        output_layout.addLayout(out_format_layout)

        output_group.setLayout(output_layout)
        layout.addWidget(output_group)

        # Options
        options_group = QGroupBox("Options")
        options_layout = QVBoxLayout()

        # Include channel info
        self.include_channel_info = QCheckBox("Include Channel Info")
        self.include_channel_info.setChecked(True)
        options_layout.addWidget(self.include_channel_info)

        # Include EPG IDs
        self.include_epg_ids = QCheckBox("Include EPG IDs")
        self.include_epg_ids.setChecked(True)
        options_layout.addWidget(self.include_epg_ids)

        # Include group titles
        self.include_group_titles = QCheckBox("Include Group Titles")
        self.include_group_titles.setChecked(True)
        options_layout.addWidget(self.include_group_titles)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # Convert button
        self.convert_button = QPushButton("Convert")
        self.convert_button.clicked.connect(self.convert_format)
        layout.addWidget(self.convert_button)

        # Progress bar
        self.converter_progress = QProgressBar()
        self.converter_progress.setRange(0, 100)
        self.converter_progress.setValue(0)
        self.converter_progress.setTextVisible(True)
        self.converter_progress.setFormat("Ready")
        layout.addWidget(self.converter_progress)

        # Status
        self.converter_status = QLabel("Ready")
        layout.addWidget(self.converter_status)

        # Results
        self.converter_results = QTextEdit()
        self.converter_results.setReadOnly(True)
        self.converter_results.setPlaceholderText("Conversion results will appear here")
        self.converter_results.setMaximumHeight(100)
        layout.addWidget(self.converter_results)

        return tab

    def create_epg_tab(self):
        """Create EPG generator tab"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # Input section
        input_group = QGroupBox("Input")
        input_layout = QVBoxLayout()

        # M3U file
        m3u_layout = QHBoxLayout()
        m3u_layout.addWidget(QLabel("M3U File:"))
        self.m3u_file = QLineEdit()
        m3u_layout.addWidget(self.m3u_file)
        m3u_browse_button = QPushButton("Browse")
        m3u_browse_button.clicked.connect(self.browse_m3u_file)
        m3u_layout.addWidget(m3u_browse_button)
        input_layout.addLayout(m3u_layout)

        # EPG sources
        sources_layout = QHBoxLayout()
        sources_layout.addWidget(QLabel("EPG Sources:"))
        self.epg_sources = QTextEdit()
        self.epg_sources.setPlaceholderText("Enter EPG source URLs, one per line")
        self.epg_sources.setMaximumHeight(100)
        sources_layout.addWidget(self.epg_sources)
        input_layout.addLayout(sources_layout)

        input_group.setLayout(input_layout)
        layout.addWidget(input_group)

        # Output section
        output_group = QGroupBox("Output")
        output_layout = QVBoxLayout()

        # Output file
        out_file_layout = QHBoxLayout()
        out_file_layout.addWidget(QLabel("Output File:"))
        self.epg_output_file = QLineEdit()
        out_file_layout.addWidget(self.epg_output_file)
        epg_browse_button = QPushButton("Browse")
        epg_browse_button.clicked.connect(self.browse_epg_output_file)
        out_file_layout.addWidget(epg_browse_button)
        output_layout.addLayout(out_file_layout)

        # Days to fetch
        days_layout = QHBoxLayout()
        days_layout.addWidget(QLabel("Days to Fetch:"))
        self.days_to_fetch = QSpinBox()
        self.days_to_fetch.setMinimum(1)
        self.days_to_fetch.setMaximum(14)
        self.days_to_fetch.setValue(7)
        days_layout.addWidget(self.days_to_fetch)
        output_layout.addLayout(days_layout)

        output_group.setLayout(output_layout)
        layout.addWidget(output_group)

        # Generate button
        self.generate_button = QPushButton("Generate EPG")
        self.generate_button.clicked.connect(self.generate_epg)
        layout.addWidget(self.generate_button)

        # Status
        self.epg_status = QLabel("Ready")
        layout.addWidget(self.epg_status)

        return tab

    def create_playlist_cleaner_tab(self):
        """Create playlist cleaner tab"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # Input section
        input_group = QGroupBox("Input")
        input_layout = QVBoxLayout()

        # Input file
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("Playlist File:"))
        self.playlist_file = QLineEdit()
        file_layout.addWidget(self.playlist_file)
        browse_button = QPushButton("Browse")
        browse_button.clicked.connect(self.browse_playlist_file)
        file_layout.addWidget(browse_button)
        input_layout.addLayout(file_layout)

        input_group.setLayout(input_layout)
        layout.addWidget(input_group)

        # Cleaning options
        options_group = QGroupBox("Cleaning Options")
        options_layout = QVBoxLayout()

        # Remove duplicates
        self.remove_duplicates = QCheckBox("Remove Duplicate Channels")
        self.remove_duplicates.setChecked(True)
        options_layout.addWidget(self.remove_duplicates)

        # Remove broken links
        self.remove_broken = QCheckBox("Remove Broken Links")
        self.remove_broken.setChecked(True)
        options_layout.addWidget(self.remove_broken)

        # Remove adult content
        self.remove_adult = QCheckBox("Remove Adult Content")
        self.remove_adult.setChecked(False)
        options_layout.addWidget(self.remove_adult)

        # Sort channels
        self.sort_channels = QCheckBox("Sort Channels by Group")
        self.sort_channels.setChecked(True)
        options_layout.addWidget(self.sort_channels)

        # Remove duplicates button
        remove_duplicates_layout = QHBoxLayout()
        self.clean_remove_duplicates_button = QPushButton("Remove Duplicates")
        self.clean_remove_duplicates_button.clicked.connect(self.remove_duplicates_from_playlist)
        remove_duplicates_layout.addWidget(self.clean_remove_duplicates_button)
        options_layout.addLayout(remove_duplicates_layout)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # Output section
        output_group = QGroupBox("Output")
        output_layout = QVBoxLayout()

        # Output file
        out_file_layout = QHBoxLayout()
        out_file_layout.addWidget(QLabel("Output File:"))
        self.clean_output_file = QLineEdit()
        out_file_layout.addWidget(self.clean_output_file)
        clean_browse_button = QPushButton("Browse")
        clean_browse_button.clicked.connect(self.browse_clean_output_file)
        out_file_layout.addWidget(clean_browse_button)
        output_layout.addLayout(out_file_layout)

        output_group.setLayout(output_layout)
        layout.addWidget(output_group)

        # Clean button
        self.clean_button = QPushButton("Clean Playlist")
        self.clean_button.clicked.connect(self.clean_playlist)
        layout.addWidget(self.clean_button)

        # Status
        self.clean_status = QLabel("Ready")
        layout.addWidget(self.clean_status)

        return tab

    def create_playlist_merger_tab(self):
        """Create playlist merger tab"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # Input section
        input_group = QGroupBox("Input Playlists")
        input_layout = QVBoxLayout()

        # Playlists
        self.playlists_text = QTextEdit()
        self.playlists_text.setPlaceholderText("Enter playlist file paths, one per line")
        input_layout.addWidget(self.playlists_text)

        # Add playlist button
        add_button = QPushButton("Add Playlist")
        add_button.clicked.connect(self.add_playlist)
        input_layout.addWidget(add_button)

        input_group.setLayout(input_layout)
        layout.addWidget(input_group)

        # Merger options
        options_group = QGroupBox("Merger Options")
        options_layout = QVBoxLayout()

        # Remove duplicates
        self.merge_remove_duplicates = QCheckBox("Remove Duplicate Channels")
        self.merge_remove_duplicates.setChecked(True)
        options_layout.addWidget(self.merge_remove_duplicates)

        # Prefix group names
        self.prefix_groups = QCheckBox("Prefix Group Names with Playlist Name")
        self.prefix_groups.setChecked(False)
        options_layout.addWidget(self.prefix_groups)

        # Sort channels
        self.merge_sort_channels = QCheckBox("Sort Channels by Group")
        self.merge_sort_channels.setChecked(True)
        options_layout.addWidget(self.merge_sort_channels)

        # Remove duplicates button
        remove_duplicates_layout = QHBoxLayout()
        self.remove_duplicates_button = QPushButton("Remove Duplicates")
        self.remove_duplicates_button.clicked.connect(self.remove_duplicates_from_playlists)
        remove_duplicates_layout.addWidget(self.remove_duplicates_button)
        options_layout.addLayout(remove_duplicates_layout)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # Output section
        output_group = QGroupBox("Output")
        output_layout = QVBoxLayout()

        # Output file
        out_file_layout = QHBoxLayout()
        out_file_layout.addWidget(QLabel("Output File:"))
        self.merge_output_file = QLineEdit()
        out_file_layout.addWidget(self.merge_output_file)
        merge_browse_button = QPushButton("Browse")
        merge_browse_button.clicked.connect(self.browse_merge_output_file)
        out_file_layout.addWidget(merge_browse_button)
        output_layout.addLayout(out_file_layout)

        output_group.setLayout(output_layout)
        layout.addWidget(output_group)

        # Merge button
        self.merge_button = QPushButton("Merge Playlists")
        self.merge_button.clicked.connect(self.merge_playlists)
        layout.addWidget(self.merge_button)

        # Status
        self.merge_status = QLabel("Ready")
        layout.addWidget(self.merge_status)

        return tab

    def create_speed_test_tab(self):
        """Create server speed test tab"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # Input section
        input_group = QGroupBox("Server")
        input_layout = QVBoxLayout()

        # Server URL
        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel("Server URL:"))
        self.server_url = QLineEdit()
        self.server_url.setPlaceholderText("http://example.com")
        url_layout.addWidget(self.server_url)
        input_layout.addLayout(url_layout)

        # Test file
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("Test File:"))
        self.test_file = QLineEdit()
        self.test_file.setPlaceholderText("/path/to/test/file.mp4")
        file_layout.addWidget(self.test_file)
        input_layout.addLayout(file_layout)

        input_group.setLayout(input_layout)
        layout.addWidget(input_group)

        # Test options
        options_group = QGroupBox("Test Options")
        options_layout = QVBoxLayout()

        # Number of tests
        tests_layout = QHBoxLayout()
        tests_layout.addWidget(QLabel("Number of Tests:"))
        self.num_tests = QSpinBox()
        self.num_tests.setMinimum(1)
        self.num_tests.setMaximum(10)
        self.num_tests.setValue(3)
        tests_layout.addWidget(self.num_tests)
        options_layout.addLayout(tests_layout)

        # Test duration
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("Test Duration (seconds):"))
        self.test_duration = QSpinBox()
        self.test_duration.setMinimum(5)
        self.test_duration.setMaximum(60)
        self.test_duration.setValue(10)
        duration_layout.addWidget(self.test_duration)
        options_layout.addLayout(duration_layout)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # Test button
        self.test_button = QPushButton("Run Speed Test")
        self.test_button.clicked.connect(self.run_speed_test)
        layout.addWidget(self.test_button)

        # Results
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout()

        self.download_speed = QLabel("Download Speed: N/A")
        results_layout.addWidget(self.download_speed)

        self.latency = QLabel("Latency: N/A")
        results_layout.addWidget(self.latency)

        self.jitter = QLabel("Jitter: N/A")
        results_layout.addWidget(self.jitter)

        results_group.setLayout(results_layout)
        layout.addWidget(results_group)

        return tab

    def create_stream_analyzer_tab(self):
        """Create stream analyzer tab"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # Input section
        input_group = QGroupBox("Stream")
        input_layout = QVBoxLayout()

        # Stream URL
        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel("Stream URL:"))
        self.stream_url = QLineEdit()
        self.stream_url.setPlaceholderText("http://example.com/stream.m3u8")
        url_layout.addWidget(self.stream_url)
        input_layout.addLayout(url_layout)

        input_group.setLayout(input_layout)
        layout.addWidget(input_group)

        # Analysis options
        options_group = QGroupBox("Analysis Options")
        options_layout = QVBoxLayout()

        # Check video
        self.check_video = QCheckBox("Check Video")
        self.check_video.setChecked(True)
        options_layout.addWidget(self.check_video)

        # Check audio
        self.check_audio = QCheckBox("Check Audio")
        self.check_audio.setChecked(True)
        options_layout.addWidget(self.check_audio)

        # Check subtitles
        self.check_subtitles = QCheckBox("Check Subtitles")
        self.check_subtitles.setChecked(True)
        options_layout.addWidget(self.check_subtitles)

        # Analysis duration
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("Analysis Duration (seconds):"))
        self.analysis_duration = QSpinBox()
        self.analysis_duration.setMinimum(5)
        self.analysis_duration.setMaximum(60)
        self.analysis_duration.setValue(10)
        duration_layout.addWidget(self.analysis_duration)
        options_layout.addLayout(duration_layout)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # Button layout
        button_layout = QHBoxLayout()

        # Analyze button
        self.analyze_button = QPushButton("Analyze Stream")
        self.analyze_button.clicked.connect(self.analyze_stream)
        button_layout.addWidget(self.analyze_button)

        # Cancel button
        self.cancel_button = QPushButton("Cancel Analysis")
        self.cancel_button.clicked.connect(self.cancel_analysis)
        self.cancel_button.setEnabled(False)
        button_layout.addWidget(self.cancel_button)

        layout.addLayout(button_layout)

        # Progress bar
        self.analysis_progress = QProgressBar()
        self.analysis_progress.setRange(0, 100)
        self.analysis_progress.setValue(0)
        self.analysis_progress.setTextVisible(True)
        self.analysis_progress.setFormat("Ready")
        layout.addWidget(self.analysis_progress)

        # Results
        self.analysis_results = QTextEdit()
        self.analysis_results.setReadOnly(True)
        self.analysis_results.setPlaceholderText("Analysis results will appear here")
        layout.addWidget(self.analysis_results)

        return tab

    # Event handlers for converter tab
    def browse_input_file(self):
        """Browse for input file"""
        # Get selected input format
        input_format = self.input_format.currentText()

        # Set file filter based on input format
        if input_format == "M3U" or input_format == "M3U8":
            file_filter = "M3U Files (*.m3u *.m3u8);;All Files (*)"
        elif input_format == "XSPF":
            file_filter = "XSPF Files (*.xspf);;All Files (*)"
        elif input_format == "XML":
            file_filter = "XML Files (*.xml);;All Files (*)"
        elif input_format == "JSON":
            file_filter = "JSON Files (*.json);;All Files (*)"
        elif input_format == "CSV":
            file_filter = "CSV Files (*.csv);;All Files (*)"
        else:
            file_filter = "All Files (*)"

        file_path, _ = QFileDialog.getOpenFileName(self, "Select Input File", "", file_filter)
        if file_path:
            self.input_file.setText(file_path)

    def browse_output_file(self):
        """Browse for output file"""
        # Get selected output format
        output_format = self.output_format.currentText()

        # Set file filter and default extension based on output format
        if output_format == "M3U":
            file_filter = "M3U Files (*.m3u);;All Files (*)"
            default_ext = ".m3u"
        elif output_format == "M3U8":
            file_filter = "M3U8 Files (*.m3u8);;All Files (*)"
            default_ext = ".m3u8"
        elif output_format == "XSPF":
            file_filter = "XSPF Files (*.xspf);;All Files (*)"
            default_ext = ".xspf"
        elif output_format == "XML":
            file_filter = "XML Files (*.xml);;All Files (*)"
            default_ext = ".xml"
        elif output_format == "JSON":
            file_filter = "JSON Files (*.json);;All Files (*)"
            default_ext = ".json"
        elif output_format == "CSV":
            file_filter = "CSV Files (*.csv);;All Files (*)"
            default_ext = ".csv"
        else:
            file_filter = "All Files (*)"
            default_ext = ""

        file_path, _ = QFileDialog.getSaveFileName(self, "Select Output File", "", file_filter)
        if file_path:
            # Add default extension if not present
            if default_ext and not file_path.lower().endswith(default_ext):
                file_path += default_ext

            self.output_file.setText(file_path)

    def on_input_format_changed(self, index):
        """Handle input format change"""
        # Clear input file if format changed
        self.input_file.clear()

        # Update status
        self.converter_status.setText("Ready")
        self.converter_progress.setValue(0)
        self.converter_progress.setFormat("Ready")
        self.converter_results.clear()

    def on_output_format_changed(self, index):
        """Handle output format change"""
        # Clear output file if format changed
        self.output_file.clear()

        # Update status
        self.converter_status.setText("Ready")
        self.converter_progress.setValue(0)
        self.converter_progress.setFormat("Ready")
        self.converter_results.clear()

    def convert_format(self):
        """Convert playlist format"""
        # Get input file
        input_file = self.input_file.text().strip()
        if not input_file:
            QMessageBox.warning(self, "Warning", "Please select an input file.")
            return

        # Check if input file exists
        if not os.path.exists(input_file):
            QMessageBox.warning(self, "Warning", f"The file '{input_file}' was not found.")
            return

        # Get output file
        output_file = self.output_file.text().strip()
        if not output_file:
            QMessageBox.warning(self, "Warning", "Please specify an output file.")
            return

        # Get formats
        input_format = self.input_format.currentText()
        output_format = self.output_format.currentText()

        # Get options
        options = {
            'include_channel_info': self.include_channel_info.isChecked(),
            'include_epg_ids': self.include_epg_ids.isChecked(),
            'include_group_titles': self.include_group_titles.isChecked()
        }

        # Clear previous results
        self.converter_results.clear()
        self.converter_results.setPlainText("Converting...\n")

        # Reset progress bar
        self.converter_progress.setValue(0)
        self.converter_progress.setFormat("Initializing...")

        # Update status
        self.converter_status.setText("Converting...")

        # Disable convert button
        self.convert_button.setEnabled(False)

        # Create format converter
        self.format_converter = FormatConverter()

        # Connect signals
        self.format_converter.status_signal.connect(self.update_converter_status)
        self.format_converter.result_signal.connect(self.display_converter_results)
        self.format_converter.progress_signal.connect(self.update_converter_progress)

        # Start conversion in a separate thread
        self.converter_thread = threading.Thread(
            target=self.format_converter.convert,
            args=(input_file, output_file, input_format, output_format, options)
        )
        self.converter_thread.daemon = True
        self.converter_thread.start()

    def update_converter_status(self, status):
        """Update converter status"""
        self.converter_status.setText(status)
        current_text = self.converter_results.toPlainText()
        self.converter_results.setPlainText(f"{current_text}\n{status}")
        # Scroll to bottom
        cursor = self.converter_results.textCursor()
        cursor.movePosition(cursor.End)
        self.converter_results.setTextCursor(cursor)

        # Update progress bar text
        self.converter_progress.setFormat(status[:30] + "..." if len(status) > 30 else status)

    def update_converter_progress(self, current, total):
        """Update converter progress"""
        if total > 0:
            percent = int((current / total) * 100)
            self.converter_progress.setValue(percent)

    def display_converter_results(self, result):
        """Display converter results"""
        # Update status
        if result['success']:
            status = f"Conversion complete: {result['channels_processed']} channels processed"
            self.converter_status.setText(status)

            # Show success message
            QMessageBox.information(
                self,
                "Conversion Complete",
                f"Successfully converted {result['channels_processed']} channels from {result['input_format']} to {result['output_format']}.\n\n"
                f"Output file: {result['output_file']}"
            )
        else:
            status = f"Conversion failed: {result['error']}"
            self.converter_status.setText(status)

            # Show error message
            QMessageBox.critical(
                self,
                "Conversion Failed",
                f"Failed to convert file: {result['error']}"
            )

        # Update progress bar
        self.converter_progress.setValue(100)
        self.converter_progress.setFormat("Complete" if result['success'] else "Failed")

        # Enable convert button
        self.convert_button.setEnabled(True)

    # Event handlers for EPG tab
    def browse_m3u_file(self):
        """Browse for M3U file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Select M3U File", "", "M3U Files (*.m3u *.m3u8);;All Files (*)")
        if file_path:
            self.m3u_file.setText(file_path)

    def browse_epg_output_file(self):
        """Browse for EPG output file"""
        file_path, _ = QFileDialog.getSaveFileName(self, "Select EPG Output File", "", "XML Files (*.xml);;All Files (*)")
        if file_path:
            self.epg_output_file.setText(file_path)

    def generate_epg(self):
        """Generate EPG"""
        QMessageBox.information(self, "Information", "EPG generation will be implemented in a future update.")

    # Event handlers for playlist cleaner tab
    def browse_playlist_file(self):
        """Browse for playlist file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Select Playlist File", "", "M3U Files (*.m3u *.m3u8);;All Files (*)")
        if file_path:
            self.playlist_file.setText(file_path)

    def browse_clean_output_file(self):
        """Browse for clean output file"""
        file_path, _ = QFileDialog.getSaveFileName(self, "Select Clean Output File", "", "M3U Files (*.m3u *.m3u8);;All Files (*)")
        if file_path:
            self.clean_output_file.setText(file_path)

    def clean_playlist(self):
        """Clean playlist"""
        QMessageBox.information(self, "Information", "Playlist cleaning will be implemented in a future update.")

    def remove_duplicates_from_playlist(self):
        """Remove duplicate entries from a single playlist"""
        playlist_path = self.playlist_file.text().strip()
        if not playlist_path:
            QMessageBox.warning(self, "Warning", "Please select a playlist file.")
            return

        # Check if file exists
        if not os.path.exists(playlist_path):
            QMessageBox.warning(self, "Warning", f"The file '{playlist_path}' was not found.")
            return

        try:
            # Read playlist content
            with open(playlist_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Count total entries
            total_entries = content.count('#EXTINF')

            if total_entries == 0:
                QMessageBox.warning(self, "Warning", "No valid IPTV entries found in the playlist.")
                return

            # Parse playlist
            lines = content.splitlines()
            entries = []
            current_entry = None

            for line in lines:
                if line.startswith('#EXTM3U'):
                    # Header line, skip
                    continue
                elif line.startswith('#EXTINF'):
                    # Start of a new entry
                    current_entry = {'info': line, 'url': None}
                elif current_entry and current_entry['url'] is None and (line.startswith('http://') or line.startswith('https://')):
                    # URL line for current entry
                    current_entry['url'] = line
                    entries.append(current_entry)
                    current_entry = None

            # Find unique entries (based on URL)
            unique_entries = {}
            for entry in entries:
                if entry['url'] and entry['url'] not in unique_entries:
                    unique_entries[entry['url']] = entry

            # Calculate duplicates
            duplicates = total_entries - len(unique_entries)

            # Show results
            QMessageBox.information(
                self,
                "Duplicate Analysis",
                f"Found {total_entries} total entries in the playlist.\n"
                f"Detected {duplicates} duplicate entries.\n"
                f"There are {len(unique_entries)} unique entries.\n\n"
                f"Use the 'Clean Playlist' button with 'Remove Duplicate Channels' checked to create a clean playlist."
            )

            # Update status
            self.clean_status.setText(f"Analysis complete: {duplicates} duplicates found in {total_entries} entries.")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error analyzing playlist: {str(e)}")

    # Event handlers for playlist merger tab
    def add_playlist(self):
        """Add playlist file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Select Playlist File", "", "M3U Files (*.m3u *.m3u8);;All Files (*)")
        if file_path:
            current_text = self.playlists_text.toPlainText()
            if current_text:
                self.playlists_text.setPlainText(current_text + "\n" + file_path)
            else:
                self.playlists_text.setPlainText(file_path)

    def browse_merge_output_file(self):
        """Browse for merge output file"""
        file_path, _ = QFileDialog.getSaveFileName(self, "Select Merge Output File", "", "M3U Files (*.m3u *.m3u8);;All Files (*)")
        if file_path:
            self.merge_output_file.setText(file_path)

    def merge_playlists(self):
        """Merge playlists"""
        QMessageBox.information(self, "Information", "Playlist merging will be implemented in a future update.")

    def remove_duplicates_from_playlists(self):
        """Remove duplicate entries from playlists"""
        playlists_text = self.playlists_text.toPlainText().strip()
        if not playlists_text:
            QMessageBox.warning(self, "Warning", "Please add at least one playlist.")
            return

        # Get playlist paths
        playlist_paths = [line.strip() for line in playlists_text.splitlines() if line.strip()]

        # Check if files exist
        missing_files = [path for path in playlist_paths if not os.path.exists(path)]
        if missing_files:
            missing_list = "\n".join(missing_files)
            QMessageBox.warning(self, "Warning", f"The following files were not found:\n{missing_list}")
            return

        # Count total entries before removing duplicates
        total_entries = 0
        unique_entries = set()

        for path in playlist_paths:
            try:
                with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # Count entries in this playlist
                entries = content.count('#EXTINF')
                total_entries += entries

                # Extract unique URLs
                lines = content.splitlines()
                for i in range(len(lines) - 1):
                    if lines[i].startswith('#EXTINF') and (lines[i+1].startswith('http://') or lines[i+1].startswith('https://')):
                        unique_entries.add(lines[i+1])
            except Exception as e:
                QMessageBox.warning(self, "Warning", f"Error reading file {path}: {str(e)}")
                return

        # Calculate duplicates
        duplicates = total_entries - len(unique_entries)

        # Show results
        QMessageBox.information(
            self,
            "Duplicate Analysis",
            f"Found {total_entries} total entries across {len(playlist_paths)} playlists.\n"
            f"Detected {duplicates} duplicate entries.\n"
            f"There are {len(unique_entries)} unique entries.\n\n"
            f"Use the 'Remove Duplicate Channels' option when merging to create a playlist without duplicates."
        )

        # Update status
        self.merge_status.setText(f"Analysis complete: {duplicates} duplicates found in {total_entries} entries.")

    # Event handlers for speed test tab
    def run_speed_test(self):
        """Run speed test"""
        QMessageBox.information(self, "Information", "Server speed testing will be implemented in a future update.")

    # Event handlers for stream analyzer tab
    def analyze_stream(self):
        """Analyze stream"""
        # Get stream URL
        url = self.stream_url.text().strip()
        if not url:
            QMessageBox.warning(self, "Warning", "Please enter a stream URL.")
            return

        # Get analysis options
        check_video = self.check_video.isChecked()
        check_audio = self.check_audio.isChecked()
        check_subtitles = self.check_subtitles.isChecked()
        duration = self.analysis_duration.value()

        # Clear previous results
        self.analysis_results.clear()
        self.analysis_results.setPlainText("Analyzing stream...\n")

        # Reset progress bar
        self.analysis_progress.setValue(0)
        self.analysis_progress.setFormat("Initializing...")

        # Update button states
        self.analyze_button.setEnabled(False)
        self.cancel_button.setEnabled(True)

        # Create stream analyzer
        self.stream_analyzer = StreamAnalyzer()

        # Connect signals
        self.stream_analyzer.status_signal.connect(self.update_analysis_status)
        self.stream_analyzer.result_signal.connect(self.display_analysis_results)
        self.stream_analyzer.progress_signal.connect(self.update_analysis_progress)

        # Start analysis in a separate thread
        self.analysis_thread = threading.Thread(
            target=self.stream_analyzer.analyze_stream,
            args=(url, check_video, check_audio, check_subtitles, duration)
        )
        self.analysis_thread.daemon = True
        self.analysis_thread.start()

    def cancel_analysis(self):
        """Cancel stream analysis"""
        if self.stream_analyzer and self.stream_analyzer.running:
            # Set flag to stop analysis
            self.stream_analyzer.running = False

            # Update UI
            self.update_analysis_status("Analysis cancelled by user")
            self.analysis_progress.setFormat("Cancelled")

            # Reset button states
            self.analyze_button.setEnabled(True)
            self.cancel_button.setEnabled(False)

    def update_analysis_status(self, status):
        """Update analysis status"""
        current_text = self.analysis_results.toPlainText()
        self.analysis_results.setPlainText(f"{current_text}\n{status}")
        # Scroll to bottom
        cursor = self.analysis_results.textCursor()
        cursor.movePosition(cursor.End)
        self.analysis_results.setTextCursor(cursor)

        # Update progress bar text
        self.analysis_progress.setFormat(status[:30] + "..." if len(status) > 30 else status)

    def update_analysis_progress(self, current, total):
        """Update analysis progress"""
        if total > 0:
            percent = int((current / total) * 100)
            self.analysis_progress.setValue(percent)

    def display_analysis_results(self, result):
        """Display analysis results"""
        # Clear previous results
        self.analysis_results.clear()

        # Format results
        output = []
        output.append(f"Stream Analysis Results for: {result['url']}")
        output.append(f"Status: {result['status']}")
        output.append(f"Analysis Time: {result['analysis_time']}")
        output.append("")

        if result['is_valid']:
            output.append(f"Format: {result['format']}")
            output.append(f"Duration: {result['duration']}")
            output.append(f"Bitrate: {result['bitrate']}")
            output.append("")

            # Video streams
            if result['video_streams']:
                output.append("Video Streams:")
                for i, stream in enumerate(result['video_streams']):
                    output.append(f"  Stream #{i+1}:")
                    output.append(f"    Codec: {stream.get('codec', 'Unknown')}")
                    if 'resolution' in stream:
                        output.append(f"    Resolution: {stream['resolution']}")
                    if 'additional_info' in stream:
                        output.append(f"    Additional Info: {stream['additional_info']}")
                output.append("")
            else:
                output.append("No video streams detected.")
                output.append("")

            # Audio streams
            if result['audio_streams']:
                output.append("Audio Streams:")
                for i, stream in enumerate(result['audio_streams']):
                    output.append(f"  Stream #{i+1}:")
                    output.append(f"    Codec: {stream.get('codec', 'Unknown')}")
                    output.append(f"    Language: {stream.get('language', 'Unknown')}")
                    if 'sample_rate' in stream:
                        output.append(f"    Sample Rate: {stream['sample_rate']}")
                    if 'additional_info' in stream:
                        output.append(f"    Additional Info: {stream['additional_info']}")
                output.append("")
            else:
                output.append("No audio streams detected.")
                output.append("")

            # Subtitle streams
            if result['subtitle_streams']:
                output.append("Subtitle Streams:")
                for i, stream in enumerate(result['subtitle_streams']):
                    output.append(f"  Stream #{i+1}:")
                    output.append(f"    Codec: {stream.get('codec', 'Unknown')}")
                    output.append(f"    Language: {stream.get('language', 'Unknown')}")
                output.append("")
            else:
                output.append("No subtitle streams detected.")
                output.append("")
        else:
            output.append(f"Error: {result.get('error', 'Unknown error')}")

        # Set results text
        self.analysis_results.setPlainText("\n".join(output))

        # Update progress bar
        self.analysis_progress.setValue(100)
        self.analysis_progress.setFormat("Complete")

        # Reset button states
        self.analyze_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
