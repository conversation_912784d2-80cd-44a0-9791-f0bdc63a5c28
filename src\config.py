"""
Configuration settings for the IPTV Checker application.
This module contains shared configuration settings used across different checkers.
"""

import os
import json
import logging

# Default configuration
DEFAULT_CONFIG = {
    # General settings
    "max_threads": 10,
    "timeout": 10,
    "auto_save": True,

    # Telegram settings - General
    "telegram_enabled": True,
    "telegram_chat_id": "242110769",

    # Telegram Bot Tokens for different modules
    "telegram_bot_tokens": {
        "xui_panel": "**********************************************",
        "m3u_iptv": "6739447128:AAGRCcgny4XuTcg4qsuWbL8lXGvsLB8e-Xo",
        "xtream_iptv": "7768187149:AAGCzeVIStgCixFf1vCz6OA9I9ph55xMRAk",
        "mac_scanner": "5281565638:AAFm_1DOQGZw7b2MJh7VUSsOQxopnTr--vc",
        "user_pass": "5281565638:AAFm_1DOQGZw7b2MJh7VUSsOQxopnTr--vc"  # نفس MAC scanner
    },

    # Default telegram bot token (for backward compatibility)
    "telegram_bot_token": "**********************************************",

    # File paths
    "error_file": "ERROR.txt",
    "log_file": "checker.log",

    # Proxy settings
    "use_proxy": False,
    "proxy_file": "",

    # Advanced settings
    "retry_count": 3,
    "backoff_factor": 0.5,
    "status_forcelist": [500, 502, 504],

    # Feature flags
    "save_errors": True,
    "debug_mode": False
}

# Config file path
CONFIG_FILE = "config.json"

# Logger
logger = logging.getLogger("iptv_checker.config")

def load_config():
    """
    Load configuration from file or create default if not exists.

    Returns:
        dict: Configuration dictionary
    """
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r') as f:
                config = json.load(f)
                logger.info("Configuration loaded from file")
                return config
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")

    # Create default config file if not exists
    save_config(DEFAULT_CONFIG)
    return DEFAULT_CONFIG

def save_config(config):
    """
    Save configuration to file.

    Args:
        config (dict): Configuration dictionary to save
    """
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=4)
        logger.info("Configuration saved to file")
    except Exception as e:
        logger.error(f"Error saving configuration: {str(e)}")

# Global configuration object
config = load_config()

def get_config(key, default=None):
    """
    Get configuration value by key.

    Args:
        key (str): Configuration key
        default: Default value if key not found

    Returns:
        Configuration value or default
    """
    return config.get(key, default)

def get_telegram_bot_token(module_name):
    """
    Get telegram bot token for specific module.

    Args:
        module_name (str): Module name (xui_panel, m3u_iptv, xtream_iptv, mac_scanner, user_pass)

    Returns:
        str: Bot token for the module
    """
    bot_tokens = config.get("telegram_bot_tokens", {})
    return bot_tokens.get(module_name, config.get("telegram_bot_token", ""))

def set_config(key, value):
    """
    Set configuration value and save to file.

    Args:
        key (str): Configuration key
        value: Configuration value
    """
    config[key] = value
    save_config(config)

def reset_config():
    """Reset configuration to default values."""
    global config
    config = DEFAULT_CONFIG.copy()
    save_config(config)
