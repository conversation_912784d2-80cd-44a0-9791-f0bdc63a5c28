"""
Format Converter module for IPTV CHECKER - TWINS.
This module provides functionality to convert between different playlist formats.
"""

import os
import re
import json
import logging
import xml.etree.ElementTree as ET
import xml.dom.minidom as minidom
import csv
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal

# Get logger
logger = logging.getLogger("iptv_checker.format_converter")

class FormatConverter(QObject):
    """Class for converting between different playlist formats"""

    # Define signals
    result_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)
    status_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False

    def convert(self, input_file, output_file, input_format, output_format, options=None):
        """
        Convert a playlist from one format to another

        Args:
            input_file (str): Path to input file
            output_file (str): Path to output file
            input_format (str): Input format (M3U, M3U8, XSPF, XML, JSON, CSV)
            output_format (str): Output format (M3U, M3U8, XSPF, XML, JSON, CSV)
            options (dict): Conversion options
                - include_channel_info (bool): Include channel info
                - include_epg_ids (bool): Include EPG IDs
                - include_group_titles (bool): Include group titles

        Returns:
            dict: Conversion results
        """
        self.running = True
        self.status_signal.emit(f"Converting {input_format} to {output_format}...")
        self.progress_signal.emit(0, 100)

        # Default options
        if options is None:
            options = {
                'include_channel_info': True,
                'include_epg_ids': True,
                'include_group_titles': True
            }

        result = {
            'input_file': input_file,
            'output_file': output_file,
            'input_format': input_format,
            'output_format': output_format,
            'success': False,
            'error': None,
            'channels_processed': 0,
            'conversion_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        try:
            # Check if input file exists
            if not os.path.exists(input_file):
                result['error'] = f"Input file not found: {input_file}"
                self.status_signal.emit(f"Error: {result['error']}")
                self.result_signal.emit(result)
                return result

            # Parse input file
            self.status_signal.emit("Parsing input file...")
            self.progress_signal.emit(10, 100)

            channels = self._parse_input_file(input_file, input_format)
            result['channels_processed'] = len(channels)

            if not channels:
                result['error'] = "No channels found in input file"
                self.status_signal.emit(f"Error: {result['error']}")
                self.result_signal.emit(result)
                return result

            self.status_signal.emit(f"Found {len(channels)} channels")
            self.progress_signal.emit(50, 100)

            # Write output file
            self.status_signal.emit("Writing output file...")
            self._write_output_file(channels, output_file, output_format, options)

            self.progress_signal.emit(100, 100)
            self.status_signal.emit("Conversion complete")

            result['success'] = True
            self.result_signal.emit(result)
            return result

        except Exception as e:
            logger.error(f"Error converting format: {str(e)}")
            result['error'] = f"Error: {str(e)}"
            self.status_signal.emit(f"Error: {str(e)}")
            self.result_signal.emit(result)
            return result

        finally:
            self.running = False

    def _parse_input_file(self, file_path, format_type):
        """Parse input file and return list of channels"""
        format_type = format_type.upper()

        if format_type in ['M3U', 'M3U8']:
            return self._parse_m3u(file_path)
        elif format_type == 'XSPF':
            return self._parse_xspf(file_path)
        elif format_type == 'XML':
            return self._parse_xml(file_path)
        elif format_type == 'JSON':
            return self._parse_json(file_path)
        elif format_type == 'CSV':
            return self._parse_csv(file_path)
        else:
            raise ValueError(f"Unsupported input format: {format_type}")

    def _write_output_file(self, channels, file_path, format_type, options):
        """Write channels to output file in specified format"""
        format_type = format_type.upper()

        if format_type in ['M3U', 'M3U8']:
            self._write_m3u(channels, file_path, options)
        elif format_type == 'XSPF':
            self._write_xspf(channels, file_path, options)
        elif format_type == 'XML':
            self._write_xml(channels, file_path, options)
        elif format_type == 'JSON':
            self._write_json(channels, file_path, options)
        elif format_type == 'CSV':
            self._write_csv(channels, file_path, options)
        else:
            raise ValueError(f"Unsupported output format: {format_type}")

    def _parse_m3u(self, file_path):
        """Parse M3U/M3U8 file and return list of channels"""
        channels = []

        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        lines = content.splitlines()
        current_channel = None

        for line in lines:
            line = line.strip()

            if not line:
                continue

            if line.startswith('#EXTM3U'):
                continue

            if line.startswith('#EXTINF:'):
                # Parse channel info
                current_channel = {'info': line, 'url': None, 'attributes': {}}

                # Extract channel name
                name_match = re.search(r'#EXTINF:[^,]*,\s*(.*)', line)
                if name_match:
                    current_channel['name'] = name_match.group(1)
                else:
                    current_channel['name'] = 'Unknown'

                # Extract attributes
                tvg_id_match = re.search(r'tvg-id="([^"]*)"', line)
                if tvg_id_match:
                    current_channel['attributes']['tvg-id'] = tvg_id_match.group(1)

                tvg_name_match = re.search(r'tvg-name="([^"]*)"', line)
                if tvg_name_match:
                    current_channel['attributes']['tvg-name'] = tvg_name_match.group(1)

                tvg_logo_match = re.search(r'tvg-logo="([^"]*)"', line)
                if tvg_logo_match:
                    current_channel['attributes']['tvg-logo'] = tvg_logo_match.group(1)

                group_title_match = re.search(r'group-title="([^"]*)"', line)
                if group_title_match:
                    current_channel['attributes']['group-title'] = group_title_match.group(1)

            elif current_channel and current_channel['url'] is None and (line.startswith('http://') or line.startswith('https://')):
                current_channel['url'] = line
                channels.append(current_channel)
                current_channel = None

        return channels

    def _parse_xspf(self, file_path):
        """Parse XSPF file and return list of channels"""
        channels = []

        try:
            tree = ET.parse(file_path)
            root = tree.getroot()

            # XSPF namespace
            ns = {'xspf': 'http://xspf.org/ns/0/'}

            # Find all tracks
            tracks = root.findall('.//xspf:track', ns)

            for track in tracks:
                channel = {'attributes': {}}

                # Get track elements
                location = track.find('xspf:location', ns)
                title = track.find('xspf:title', ns)
                image = track.find('xspf:image', ns)

                if location is not None and location.text:
                    channel['url'] = location.text
                else:
                    continue  # Skip tracks without URL

                if title is not None and title.text:
                    channel['name'] = title.text
                else:
                    channel['name'] = 'Unknown'

                if image is not None and image.text:
                    channel['attributes']['tvg-logo'] = image.text

                # Check for custom attributes in extension
                extension = track.find('xspf:extension', ns)
                if extension is not None:
                    for attr in extension:
                        if attr.tag.endswith('group-title'):
                            channel['attributes']['group-title'] = attr.text
                        elif attr.tag.endswith('tvg-id'):
                            channel['attributes']['tvg-id'] = attr.text

                channels.append(channel)

        except Exception as e:
            logger.error(f"Error parsing XSPF file: {str(e)}")
            raise

        return channels

    def _parse_xml(self, file_path):
        """Parse XML file and return list of channels"""
        channels = []

        try:
            tree = ET.parse(file_path)
            root = tree.getroot()

            # Find all channel elements
            channel_elements = root.findall('.//channel')

            for channel_elem in channel_elements:
                channel = {'attributes': {}}

                # Get channel attributes
                channel['name'] = channel_elem.get('name', 'Unknown')
                channel['url'] = channel_elem.get('url', '')

                if not channel['url']:
                    # Try to find URL in a child element
                    url_elem = channel_elem.find('url')
                    if url_elem is not None and url_elem.text:
                        channel['url'] = url_elem.text
                    else:
                        continue  # Skip channels without URL

                # Get other attributes
                for attr in ['tvg-id', 'tvg-name', 'tvg-logo', 'group-title']:
                    value = channel_elem.get(attr, '')
                    if value:
                        channel['attributes'][attr] = value
                    else:
                        # Try to find attribute in a child element
                        attr_elem = channel_elem.find(attr.replace('-', '_'))
                        if attr_elem is not None and attr_elem.text:
                            channel['attributes'][attr] = attr_elem.text

                channels.append(channel)

        except Exception as e:
            logger.error(f"Error parsing XML file: {str(e)}")
            raise

        return channels

    def _parse_json(self, file_path):
        """Parse JSON file and return list of channels"""
        channels = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Check if it's an array of channels or has a channels property
            if isinstance(data, list):
                channel_list = data
            elif isinstance(data, dict) and 'channels' in data:
                channel_list = data['channels']
            else:
                raise ValueError("Invalid JSON format: expected array of channels or object with 'channels' property")

            for item in channel_list:
                channel = {'attributes': {}}

                # Get required properties
                if 'name' in item:
                    channel['name'] = item['name']
                else:
                    channel['name'] = 'Unknown'

                if 'url' in item:
                    channel['url'] = item['url']
                else:
                    continue  # Skip channels without URL

                # Get attributes
                if 'attributes' in item and isinstance(item['attributes'], dict):
                    channel['attributes'] = item['attributes']
                else:
                    # Try to find individual attributes
                    for attr in ['tvg-id', 'tvg-name', 'tvg-logo', 'group-title']:
                        if attr in item:
                            channel['attributes'][attr] = item[attr]
                        # Try with underscores instead of hyphens
                        elif attr.replace('-', '_') in item:
                            channel['attributes'][attr] = item[attr.replace('-', '_')]

                channels.append(channel)

        except Exception as e:
            logger.error(f"Error parsing JSON file: {str(e)}")
            raise

        return channels

    def _parse_csv(self, file_path):
        """Parse CSV file and return list of channels"""
        channels = []

        try:
            with open(file_path, 'r', encoding='utf-8', newline='') as f:
                reader = csv.DictReader(f)

                for row in reader:
                    channel = {'attributes': {}}

                    # Get name and URL
                    if 'name' in row:
                        channel['name'] = row['name']
                    else:
                        channel['name'] = 'Unknown'

                    if 'url' in row:
                        channel['url'] = row['url']
                    else:
                        continue  # Skip channels without URL

                    # Get attributes
                    for attr in ['tvg-id', 'tvg-name', 'tvg-logo', 'group-title']:
                        if attr in row and row[attr]:
                            channel['attributes'][attr] = row[attr]
                        # Try with underscores instead of hyphens
                        elif attr.replace('-', '_') in row and row[attr.replace('-', '_')]:
                            channel['attributes'][attr] = row[attr.replace('-', '_')]

                    channels.append(channel)

        except Exception as e:
            logger.error(f"Error parsing CSV file: {str(e)}")
            raise

        return channels

    def _write_m3u(self, channels, file_path, options):
        """Write channels to M3U/M3U8 file"""
        with open(file_path, 'w', encoding='utf-8') as f:
            # Write header
            f.write('#EXTM3U\n')

            # Write channels
            for channel in channels:
                # Build EXTINF line
                extinf = '#EXTINF:-1'

                # Add attributes if requested
                if options.get('include_epg_ids', True) and 'attributes' in channel:
                    if 'tvg-id' in channel['attributes']:
                        extinf += f' tvg-id="{channel["attributes"]["tvg-id"]}"'
                    if 'tvg-name' in channel['attributes']:
                        extinf += f' tvg-name="{channel["attributes"]["tvg-name"]}"'
                    if 'tvg-logo' in channel['attributes']:
                        extinf += f' tvg-logo="{channel["attributes"]["tvg-logo"]}"'

                # Add group-title if requested
                if options.get('include_group_titles', True) and 'attributes' in channel and 'group-title' in channel['attributes']:
                    extinf += f' group-title="{channel["attributes"]["group-title"]}"'

                # Add channel name
                extinf += f',{channel.get("name", "Unknown")}'

                # Write EXTINF line and URL
                f.write(f'{extinf}\n')
                f.write(f'{channel["url"]}\n')

    def _write_xspf(self, channels, file_path, options):
        """Write channels to XSPF file"""
        # Create root element
        root = ET.Element('playlist')
        root.set('version', '1')
        root.set('xmlns', 'http://xspf.org/ns/0/')

        # Create title element
        title = ET.SubElement(root, 'title')
        title.text = 'IPTV Playlist'

        # Create trackList element
        track_list = ET.SubElement(root, 'trackList')

        # Add tracks
        for channel in channels:
            track = ET.SubElement(track_list, 'track')

            # Add location (URL)
            location = ET.SubElement(track, 'location')
            location.text = channel['url']

            # Add title (name)
            if options.get('include_channel_info', True):
                title = ET.SubElement(track, 'title')
                title.text = channel.get('name', 'Unknown')

            # Add image (logo)
            if options.get('include_channel_info', True) and 'attributes' in channel and 'tvg-logo' in channel['attributes']:
                image = ET.SubElement(track, 'image')
                image.text = channel['attributes']['tvg-logo']

            # Add custom attributes in extension
            if (options.get('include_epg_ids', True) or options.get('include_group_titles', True)) and 'attributes' in channel:
                extension = ET.SubElement(track, 'extension')
                extension.set('application', 'http://iptv.checker.twins')

                if options.get('include_epg_ids', True) and 'tvg-id' in channel['attributes']:
                    tvg_id = ET.SubElement(extension, 'tvg_id')
                    tvg_id.text = channel['attributes']['tvg-id']

                if options.get('include_group_titles', True) and 'group-title' in channel['attributes']:
                    group_title = ET.SubElement(extension, 'group_title')
                    group_title.text = channel['attributes']['group-title']

        # Write to file with pretty formatting
        tree = ET.ElementTree(root)
        xml_str = minidom.parseString(ET.tostring(root)).toprettyxml(indent="  ")

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(xml_str)

    def _write_xml(self, channels, file_path, options):
        """Write channels to XML file"""
        # Create root element
        root = ET.Element('channels')

        # Add channels
        for channel in channels:
            channel_elem = ET.SubElement(root, 'channel')

            # Add name and URL attributes
            if options.get('include_channel_info', True):
                channel_elem.set('name', channel.get('name', 'Unknown'))
            channel_elem.set('url', channel['url'])

            # Add other attributes
            if 'attributes' in channel:
                if options.get('include_epg_ids', True):
                    for attr in ['tvg-id', 'tvg-name', 'tvg-logo']:
                        if attr in channel['attributes']:
                            channel_elem.set(attr, channel['attributes'][attr])

                if options.get('include_group_titles', True) and 'group-title' in channel['attributes']:
                    channel_elem.set('group-title', channel['attributes']['group-title'])

        # Write to file with pretty formatting
        tree = ET.ElementTree(root)
        xml_str = minidom.parseString(ET.tostring(root)).toprettyxml(indent="  ")

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(xml_str)

    def _write_json(self, channels, file_path, options):
        """Write channels to JSON file"""
        output_channels = []

        for channel in channels:
            output_channel = {
                'url': channel['url']
            }

            # Add name if requested
            if options.get('include_channel_info', True):
                output_channel['name'] = channel.get('name', 'Unknown')

            # Add attributes if requested
            if 'attributes' in channel:
                output_channel['attributes'] = {}

                if options.get('include_epg_ids', True):
                    for attr in ['tvg-id', 'tvg-name', 'tvg-logo']:
                        if attr in channel['attributes']:
                            output_channel['attributes'][attr] = channel['attributes'][attr]

                if options.get('include_group_titles', True) and 'group-title' in channel['attributes']:
                    output_channel['attributes']['group-title'] = channel['attributes']['group-title']

            output_channels.append(output_channel)

        # Create output object
        output = {
            'channels': output_channels,
            'generated': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'count': len(output_channels)
        }

        # Write to file with pretty formatting
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(output, f, indent=2, ensure_ascii=False)

    def _write_csv(self, channels, file_path, options):
        """Write channels to CSV file"""
        fieldnames = ['name', 'url']

        # Add attribute fieldnames if requested
        if options.get('include_epg_ids', True):
            fieldnames.extend(['tvg-id', 'tvg-name', 'tvg-logo'])

        if options.get('include_group_titles', True):
            fieldnames.append('group-title')

        with open(file_path, 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()

            for channel in channels:
                row = {'url': channel['url']}

                # Add name if requested
                if options.get('include_channel_info', True):
                    row['name'] = channel.get('name', 'Unknown')

                # Add attributes if requested
                if 'attributes' in channel:
                    if options.get('include_epg_ids', True):
                        for attr in ['tvg-id', 'tvg-name', 'tvg-logo']:
                            if attr in channel['attributes']:
                                row[attr] = channel['attributes'][attr]

                    if options.get('include_group_titles', True) and 'group-title' in channel['attributes']:
                        row['group-title'] = channel['attributes']['group-title']

                writer.writerow(row)