# خيارات IPTV المتاحة في التطبيق

## الخيارات الحالية

### 1. MAC Scanner
أداة لفحص عناوين MAC وتحديد ما إذا كانت نشطة. تستخدم هذه الأداة لفحص عناوين MAC للأجهزة المتصلة بخدمات IPTV.

### 2. USER-PASS
أداة للتحقق من صحة بيانات اعتماد المستخدم (اسم المستخدم وكلمة المرور) للوصول إلى خدمات IPTV.

### 3. M3U CHECKER
أداة للتحقق من صحة وعمل روابط M3U. تقوم بفحص روابط قوائم التشغيل M3U للتأكد من أنها تعمل بشكل صحيح.

### 4. XTREAM CODE
نظام Xtream Codes هو واجهة برمجة تطبيقات (API) تستخدم لإدارة خدمات IPTV. يتيح للمستخدمين الوصول إلى البث المباشر والمحتوى المسجل باستخدام بروتوكول HTTP.

**ميزات Xtream Codes:**
- دعم البث المباشر للقنوات التلفزيونية
- دعم المحتوى المسجل (VOD)
- دعم المسلسلات التلفزيونية
- إدارة المستخدمين والاشتراكات
- واجهة برمجة تطبيقات (API) قوية
- توافق مع معظم تطبيقات IPTV

### 5. XUI PANEL
XUI Panel هو واجهة إدارة لخوادم IPTV. يوفر واجهة مستخدم رسومية لإدارة خدمات IPTV، بما في ذلك إدارة المستخدمين والقنوات والمحتوى.

**ميزات XUI Panel:**
- إدارة المستخدمين والاشتراكات
- إدارة القنوات والمجموعات
- إدارة المحتوى المسجل (VOD)
- إحصاءات وتقارير مفصلة
- واجهة مستخدم سهلة الاستخدام
- دعم متعدد اللغات

### 6. PORTAL EXTRACTOR
أداة لاستخراج والتحقق من بوابات IPTV. تقوم بالبحث عن بوابات IPTV المحتملة والتحقق من صحتها.

## الخيارات الإضافية (غير مُنفذة حالياً)

### 1. STALKER PLAYER
Stalker Portal هو نظام إدارة IPTV آخر يستخدم بروتوكول مختلف عن Xtream Codes. يوفر واجهة برمجة تطبيقات (API) للوصول إلى المحتوى وإدارته.

**ميزات Stalker Portal:**
- دعم أجهزة استقبال MAG
- نظام إدارة مستخدمين متقدم
- دعم EPG (دليل البرامج الإلكتروني)
- دعم التحكم في الوصول
- دعم المحتوى المسجل (VOD)
- دعم المسلسلات التلفزيونية

### 2. IPTV PLAYER
مشغل مدمج لمشاهدة قنوات IPTV مباشرة من التطبيق. يتيح للمستخدمين مشاهدة القنوات التي تم التحقق منها دون الحاجة إلى تطبيق خارجي.

**ميزات IPTV Player المحتملة:**
- تشغيل قنوات البث المباشر
- دعم EPG (دليل البرامج الإلكتروني)
- قائمة المفضلة
- تصنيف القنوات
- التحكم في الصوت والفيديو
- دعم الترجمة

### 3. TOOLS
مجموعة من الأدوات المساعدة لإدارة وتحسين تجربة IPTV.

**أدوات محتملة:**
- محول تنسيقات (M3U إلى XML، إلخ)
- مولد EPG (دليل البرامج الإلكتروني)
- أداة تنظيف قوائم التشغيل
- أداة دمج قوائم التشغيل
- فاحص سرعة الخادم
- أداة تحليل جودة البث

## أنظمة IPTV الأخرى التي يمكن دعمها

### 1. Simple IPTV
نظام بسيط لإدارة IPTV يعتمد على ملفات M3U وEPG XML.

### 2. Ministra
نظام إدارة IPTV متقدم يدعم أجهزة استقبال متعددة ويوفر ميزات متقدمة لإدارة المحتوى.

### 3. Enigma2
نظام تشغيل مفتوح المصدر لأجهزة استقبال الأقمار الصناعية الرقمية، يدعم IPTV من خلال إضافات مختلفة.

### 4. TVHeadend
خادم تلفزيون وتسجيل مفتوح المصدر يدعم IPTV ويمكن استخدامه كوسيط بين مصادر IPTV وتطبيقات العميل.

### 5. Emby/Jellyfin/Plex
أنظمة إدارة وسائط تدعم IPTV من خلال إضافات مختلفة، توفر واجهة مستخدم غنية وميزات متقدمة.

## ملاحظات
- بعض هذه الأنظمة قد تتطلب تراخيص أو اشتراكات للاستخدام الكامل.
- قد تختلف ميزات وقدرات كل نظام اعتمادًا على الإصدار والتكوين.
- يجب استخدام هذه الأنظمة وفقًا للقوانين واللوائح المحلية المتعلقة بحقوق الملكية الفكرية والبث.
