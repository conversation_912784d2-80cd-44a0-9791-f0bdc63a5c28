import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QCheckBox, QSpinBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFileDialog, QProgressBar,
                            QGroupBox, QRadioButton, QMessageBox, QComboBox, QTabWidget,
                            QStackedWidget)
from PyQt5.QtGui import QColor
from PyQt5.QtCore import Qt, pyqtSlot
from styles import get_status_color

class IPTVSystemsTab(QWidget):
    """Tab for various IPTV systems"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """Initialize UI components"""
        # Main layout
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)

        # System selection
        system_layout = QHBoxLayout()
        system_layout.addWidget(QLabel("IPTV System:"))
        self.system_selector = QComboBox()
        self.system_selector.addItems([
            "Simple IPTV",
            "Ministra",
            "Enigma2",
            "TVHeadend",
            "Media Server (Emby/Jellyfin/Plex)"
        ])
        self.system_selector.currentIndexChanged.connect(self.change_system)
        system_layout.addWidget(self.system_selector)

        # File input section
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("External File:"))
        self.file_input = QLineEdit()
        self.file_input.setPlaceholderText("Select a file to check")
        file_layout.addWidget(self.file_input)

        browse_button = QPushButton("Browse")
        browse_button.clicked.connect(self.browse_file)
        file_layout.addWidget(browse_button)

        system_layout.addLayout(file_layout)
        main_layout.addLayout(system_layout)

        # Stacked widget for different system UIs
        self.system_stack = QStackedWidget()

        # Add system UIs to stack
        self.system_stack.addWidget(self.create_simple_iptv_ui())
        self.system_stack.addWidget(self.create_ministra_ui())
        self.system_stack.addWidget(self.create_enigma2_ui())
        self.system_stack.addWidget(self.create_tvheadend_ui())
        self.system_stack.addWidget(self.create_media_server_ui())

        main_layout.addWidget(self.system_stack)

        # Action buttons
        action_layout = QHBoxLayout()

        # Test connection button
        self.test_button = QPushButton("Test Connection")
        self.test_button.clicked.connect(self.test_connection)
        action_layout.addWidget(self.test_button)

        # Start button
        self.start_button = QPushButton("Start Check")
        self.start_button.clicked.connect(self.start_check)
        action_layout.addWidget(self.start_button)

        # Stop button
        self.stop_button = QPushButton("Stop Check")
        self.stop_button.clicked.connect(self.stop_check)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)

        # Clear button
        self.clear_button = QPushButton("Clear Results")
        self.clear_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_button)

        # Remove Duplicates button
        self.remove_duplicates_button = QPushButton("Remove Duplicates")
        self.remove_duplicates_button.clicked.connect(self.remove_duplicates)
        action_layout.addWidget(self.remove_duplicates_button)

        main_layout.addLayout(action_layout)

        # Progress section
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(QLabel("Progress:"))
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        self.status_label = QLabel("Ready")
        progress_layout.addWidget(self.status_label)
        main_layout.addLayout(progress_layout)

        # Results table
        self.results_table = QTableWidget(0, 5)
        self.results_table.setHorizontalHeaderLabels(["Name", "Type", "Status", "Info", "URL"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        main_layout.addWidget(self.results_table)

    def create_simple_iptv_ui(self):
        """Create Simple IPTV UI"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)

        # Configuration section
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout()

        # M3U URL input
        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel("M3U URL:"))
        self.m3u_url_input = QLineEdit()
        self.m3u_url_input.setPlaceholderText("http://example.com/playlist.m3u")
        url_layout.addWidget(self.m3u_url_input)
        config_layout.addLayout(url_layout)

        # EPG URL input
        epg_layout = QHBoxLayout()
        epg_layout.addWidget(QLabel("EPG URL (optional):"))
        self.epg_url_input = QLineEdit()
        self.epg_url_input.setPlaceholderText("http://example.com/epg.xml")
        epg_layout.addWidget(self.epg_url_input)
        config_layout.addLayout(epg_layout)

        # Options
        options_layout = QHBoxLayout()

        # Verify channels checkbox
        self.verify_channels = QCheckBox("Verify Channels")
        self.verify_channels.setChecked(True)
        options_layout.addWidget(self.verify_channels)

        # Filter expired checkbox
        self.filter_expired = QCheckBox("Filter Expired")
        self.filter_expired.setChecked(True)
        options_layout.addWidget(self.filter_expired)

        # Auto-save checkbox
        self.simple_auto_save = QCheckBox("Auto-save Results")
        self.simple_auto_save.setChecked(True)
        options_layout.addWidget(self.simple_auto_save)

        config_layout.addLayout(options_layout)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        return widget

    def create_ministra_ui(self):
        """Create Ministra UI"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)

        # Configuration section
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout()

        # Portal URL input
        portal_layout = QHBoxLayout()
        portal_layout.addWidget(QLabel("Portal URL:"))
        self.portal_input = QLineEdit()
        self.portal_input.setPlaceholderText("http://example.com/stalker_portal")
        portal_layout.addWidget(self.portal_input)
        config_layout.addLayout(portal_layout)

        # Credentials section
        creds_layout = QHBoxLayout()

        # Username input
        creds_layout.addWidget(QLabel("Username:"))
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("admin")
        creds_layout.addWidget(self.username_input)

        # Password input
        creds_layout.addWidget(QLabel("Password:"))
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("password")
        self.password_input.setEchoMode(QLineEdit.Password)
        creds_layout.addWidget(self.password_input)

        config_layout.addLayout(creds_layout)

        # Options
        options_layout = QHBoxLayout()

        # Check users checkbox
        self.check_users = QCheckBox("Check Users")
        self.check_users.setChecked(True)
        options_layout.addWidget(self.check_users)

        # Check channels checkbox
        self.check_channels = QCheckBox("Check Channels")
        self.check_channels.setChecked(True)
        options_layout.addWidget(self.check_channels)

        # Auto-save checkbox
        self.ministra_auto_save = QCheckBox("Auto-save Results")
        self.ministra_auto_save.setChecked(True)
        options_layout.addWidget(self.ministra_auto_save)

        config_layout.addLayout(options_layout)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        return widget

    def create_enigma2_ui(self):
        """Create Enigma2 UI"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)

        # Configuration section
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout()

        # Device URL input
        device_layout = QHBoxLayout()
        device_layout.addWidget(QLabel("Device URL:"))
        self.device_input = QLineEdit()
        self.device_input.setPlaceholderText("http://192.168.1.100")
        device_layout.addWidget(self.device_input)
        config_layout.addLayout(device_layout)

        # Credentials section
        creds_layout = QHBoxLayout()

        # Username input
        creds_layout.addWidget(QLabel("Username:"))
        self.enigma_username_input = QLineEdit()
        self.enigma_username_input.setPlaceholderText("root")
        creds_layout.addWidget(self.enigma_username_input)

        # Password input
        creds_layout.addWidget(QLabel("Password:"))
        self.enigma_password_input = QLineEdit()
        self.enigma_password_input.setPlaceholderText("password")
        self.enigma_password_input.setEchoMode(QLineEdit.Password)
        creds_layout.addWidget(self.enigma_password_input)

        config_layout.addLayout(creds_layout)

        # Options
        options_layout = QHBoxLayout()

        # Check bouquets checkbox
        self.check_bouquets = QCheckBox("Check Bouquets")
        self.check_bouquets.setChecked(True)
        options_layout.addWidget(self.check_bouquets)

        # Auto-save checkbox
        self.enigma_auto_save = QCheckBox("Auto-save Results")
        self.enigma_auto_save.setChecked(True)
        options_layout.addWidget(self.enigma_auto_save)

        config_layout.addLayout(options_layout)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        return widget

    def create_tvheadend_ui(self):
        """Create TVHeadend UI"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)

        # Configuration section
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout()

        # Server URL input
        server_layout = QHBoxLayout()
        server_layout.addWidget(QLabel("Server URL:"))
        self.server_input = QLineEdit()
        self.server_input.setPlaceholderText("http://192.168.1.100:9981")
        server_layout.addWidget(self.server_input)
        config_layout.addLayout(server_layout)

        # Credentials section
        creds_layout = QHBoxLayout()

        # Username input
        creds_layout.addWidget(QLabel("Username:"))
        self.tvh_username_input = QLineEdit()
        self.tvh_username_input.setPlaceholderText("admin")
        creds_layout.addWidget(self.tvh_username_input)

        # Password input
        creds_layout.addWidget(QLabel("Password:"))
        self.tvh_password_input = QLineEdit()
        self.tvh_password_input.setPlaceholderText("password")
        self.tvh_password_input.setEchoMode(QLineEdit.Password)
        creds_layout.addWidget(self.tvh_password_input)

        config_layout.addLayout(creds_layout)

        # Options
        options_layout = QHBoxLayout()

        # Check networks checkbox
        self.check_networks = QCheckBox("Check Networks")
        self.check_networks.setChecked(True)
        options_layout.addWidget(self.check_networks)

        # Check services checkbox
        self.check_services = QCheckBox("Check Services")
        self.check_services.setChecked(True)
        options_layout.addWidget(self.check_services)

        # Auto-save checkbox
        self.tvh_auto_save = QCheckBox("Auto-save Results")
        self.tvh_auto_save.setChecked(True)
        options_layout.addWidget(self.tvh_auto_save)

        config_layout.addLayout(options_layout)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        return widget

    def create_media_server_ui(self):
        """Create Media Server UI"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)

        # Configuration section
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout()

        # Server type selection
        server_type_layout = QHBoxLayout()
        server_type_layout.addWidget(QLabel("Server Type:"))
        self.server_type = QComboBox()
        self.server_type.addItems(["Emby", "Jellyfin", "Plex"])
        server_type_layout.addWidget(self.server_type)
        config_layout.addLayout(server_type_layout)

        # Server URL input
        server_layout = QHBoxLayout()
        server_layout.addWidget(QLabel("Server URL:"))
        self.media_server_input = QLineEdit()
        self.media_server_input.setPlaceholderText("http://192.168.1.100:8096")
        server_layout.addWidget(self.media_server_input)
        config_layout.addLayout(server_layout)

        # Credentials section
        creds_layout = QHBoxLayout()

        # Username input
        creds_layout.addWidget(QLabel("Username:"))
        self.media_username_input = QLineEdit()
        self.media_username_input.setPlaceholderText("admin")
        creds_layout.addWidget(self.media_username_input)

        # Password input
        creds_layout.addWidget(QLabel("Password:"))
        self.media_password_input = QLineEdit()
        self.media_password_input.setPlaceholderText("password")
        self.media_password_input.setEchoMode(QLineEdit.Password)
        creds_layout.addWidget(self.media_password_input)

        # API key input
        creds_layout.addWidget(QLabel("API Key:"))
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("(Optional)")
        creds_layout.addWidget(self.api_key_input)

        config_layout.addLayout(creds_layout)

        # Options
        options_layout = QHBoxLayout()

        # Check libraries checkbox
        self.check_libraries = QCheckBox("Check Libraries")
        self.check_libraries.setChecked(True)
        options_layout.addWidget(self.check_libraries)

        # Auto-save checkbox
        self.media_auto_save = QCheckBox("Auto-save Results")
        self.media_auto_save.setChecked(True)
        options_layout.addWidget(self.media_auto_save)

        config_layout.addLayout(options_layout)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        return widget

    def change_system(self, index):
        """Change the current system UI"""
        self.system_stack.setCurrentIndex(index)
        self.clear_results()
        self.status_label.setText("Ready")

    def browse_file(self):
        """Browse for external file to check"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Select File", "", "All Files (*)")
        if file_path:
            self.file_input.setText(file_path)

    def test_connection(self):
        """Test connection to the selected system"""
        system = self.system_selector.currentText()
        QMessageBox.information(self, "Information", f"{system} functionality will be implemented in a future update.")

    def start_check(self):
        """Start checking the selected system"""
        system = self.system_selector.currentText()

        # Check if external file is provided
        file_path = self.file_input.text().strip()
        if file_path:
            if not os.path.exists(file_path):
                QMessageBox.warning(self, "Warning", f"File not found: {file_path}")
                return

            QMessageBox.information(self, "Information", f"File checking for {system} will be implemented in a future update.")
            return

        QMessageBox.information(self, "Information", f"{system} functionality will be implemented in a future update.")

    def stop_check(self):
        """Stop checking"""
        self.status_label.setText("Check stopped")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)
        self.status_label.setText("Results cleared")

    def remove_duplicates(self):
        """Remove duplicate entries from results table"""
        if self.results_table.rowCount() == 0:
            self.status_label.setText("No results to process")
            return

        # Get all values from the table
        values = []
        for row in range(self.results_table.rowCount()):
            row_data = {}
            for col in range(self.results_table.columnCount()):
                item = self.results_table.item(row, col)
                if item:
                    row_data[col] = item.text()
                else:
                    row_data[col] = ""
            values.append(row_data)

        # Find unique values (based on URL column)
        unique_values = {}
        for data in values:
            # Use URL (column 4) as the key for uniqueness
            key = data.get(4, "")
            if key and key not in unique_values:
                unique_values[key] = data

        # Clear table and add unique values
        self.results_table.setRowCount(0)
        for data in unique_values.values():
            row = self.results_table.rowCount()
            self.results_table.insertRow(row)

            for col, text in data.items():
                self.results_table.setItem(row, col, QTableWidgetItem(text))

        # Update status
        removed = len(values) - len(unique_values)
        self.status_label.setText(f"Removed {removed} duplicate entries. {len(unique_values)} unique entries remaining.")
