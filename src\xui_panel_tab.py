import os
import json
import csv
import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QCheckBox, QSpinBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFileDialog, QProgressBar,
                            QComboBox, QMenu, QAction, QMessageBox, QToolButton, QFrame)
from PyQt5.QtCore import Qt, pyqtSlot, QSize
from PyQt5.QtGui import QColor, QIcon, QFont
from xui_panel_checker import XUIPanel<PERSON>hecker
from config import get_config, get_telegram_bot_token

class XUIPanelTab(QWidget):
    def __init__(self):
        super().__init__()

        # Create checker
        self.checker = XUIPanelChecker()
        self.checker.result_signal.connect(self.on_result)
        self.checker.progress_signal.connect(self.on_progress)
        self.checker.status_signal.connect(self.on_status)

        # Initialize UI
        self.init_ui()

        # Load default values from config
        self.load_default_values()

        # Update checker settings with loaded values
        self.update_settings()

        # Connect signals
        self.connect_signals()

    def init_ui(self):
        """Initialize UI components"""
        # Main layout
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)

        # Set modern style with improved colors
        self.setStyleSheet("""
            QWidget {
                background-color: #2d3250;
                color: white;
                font-size: 10pt;
            }
            QLabel {
                font-size: 10pt;
                color: white;
            }
            QPushButton {
                background-color: #4a5de8;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover { background-color: #5a6df8; }
            QPushButton:pressed { background-color: #3a4dd8; }
            QCheckBox {
                font-size: 10pt;
                color: white;
            }
            QSpinBox {
                padding: 5px;
                background-color: #3d4270;
                color: white;
                border: 1px solid #5a6df8;
                border-radius: 3px;
            }
            QLineEdit {
                padding: 8px;
                background-color: #3d4270;
                color: white;
                border: 1px solid #5a6df8;
                border-radius: 3px;
                font-size: 11pt;
            }
            QTextEdit {
                padding: 5px;
                background-color: #3d4270;
                color: white;
                border: 1px solid #5a6df8;
            }
            QTableWidget {
                alternate-background-color: #3d4270;
                gridline-color: #5a6df8;
                background-color: #2d3250;
                color: white;
                border: 1px solid #5a6df8;
            }
            QHeaderView::section {
                background-color: #4a5de8;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
            QProgressBar {
                border: 1px solid #5a6df8;
                border-radius: 5px;
                text-align: center;
                background-color: #3d4270;
                color: white;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4a5de8;
                width: 10px;
                margin: 0.5px;
            }
            QComboBox {
                padding: 5px;
                background-color: #3d4270;
                color: white;
                border: 1px solid #5a6df8;
                border-radius: 3px;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 15px;
                border-left-width: 1px;
                border-left-color: #5a6df8;
                border-left-style: solid;
            }
            QComboBox QAbstractItemView {
                background-color: #3d4270;
                color: white;
                selection-background-color: #4a5de8;
            }
            QFrame {
                background-color: #2d3250;
                border: 1px solid #5a6df8;
                border-radius: 5px;
            }
        """)

        # Configuration section
        config_frame = QFrame()
        config_frame.setFrameShape(QFrame.StyledPanel)
        config_layout = QVBoxLayout(config_frame)

        config_label = QLabel("Configuration")
        config_label.setStyleSheet("font-weight: bold; font-size: 12pt; color: #5a6df8;")
        config_layout.addWidget(config_label)

        # URLs section
        urls_layout = QVBoxLayout()
        urls_label = QLabel("URLs:")
        urls_label.setStyleSheet("font-weight: bold;")
        urls_layout.addWidget(urls_label)

        self.urls_text = QTextEdit()
        self.urls_text.setPlaceholderText("Enter URLs in format http://example.com:8080/get.php?username=user&password=pass, one per line")
        urls_layout.addWidget(self.urls_text)

        # Load URLs button
        load_urls_layout = QHBoxLayout()
        self.load_urls_btn = QPushButton("Load URLs")
        self.load_urls_btn.setIcon(QIcon.fromTheme("document-open"))

        self.clear_urls_btn = QPushButton("Clear")
        self.clear_urls_btn.setIcon(QIcon.fromTheme("edit-clear"))

        self.remove_duplicates_btn = QPushButton("Remove Duplicates")
        self.remove_duplicates_btn.setIcon(QIcon.fromTheme("edit-delete"))

        load_urls_layout.addWidget(self.load_urls_btn)
        load_urls_layout.addWidget(self.clear_urls_btn)
        load_urls_layout.addWidget(self.remove_duplicates_btn)
        urls_layout.addLayout(load_urls_layout)

        config_layout.addLayout(urls_layout)

        # Proxy section
        proxy_layout = QHBoxLayout()
        self.use_proxy_cb = QCheckBox("Use Proxy")
        self.load_proxy_btn = QPushButton("Load Proxy List")
        self.load_proxy_btn.setIcon(QIcon.fromTheme("document-open"))

        proxy_count_label = QLabel("Proxies: 0")
        self.proxy_count_label = proxy_count_label
        proxy_layout.addWidget(self.use_proxy_cb)
        proxy_layout.addWidget(self.load_proxy_btn)
        proxy_layout.addWidget(proxy_count_label)
        proxy_layout.addStretch()
        config_layout.addLayout(proxy_layout)

        # Thread and timeout settings
        settings_layout = QHBoxLayout()
        max_threads_label = QLabel("Max Threads:")
        self.max_threads_spin = QSpinBox()
        self.max_threads_spin.setRange(1, 100)
        self.max_threads_spin.setValue(10)
        timeout_label = QLabel("Timeout (sec):")
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(1, 60)
        self.timeout_spin.setValue(5)
        settings_layout.addWidget(max_threads_label)
        settings_layout.addWidget(self.max_threads_spin)
        settings_layout.addWidget(timeout_label)
        settings_layout.addWidget(self.timeout_spin)
        settings_layout.addStretch()
        config_layout.addLayout(settings_layout)

        # Auto-save option
        self.auto_save_cb = QCheckBox("Auto-save good results to file")
        self.auto_save_cb.setChecked(True)
        config_layout.addWidget(self.auto_save_cb)

        main_layout.addWidget(config_frame)

        # Telegram settings
        telegram_frame = QFrame()
        telegram_frame.setFrameShape(QFrame.StyledPanel)
        telegram_layout = QVBoxLayout(telegram_frame)

        telegram_label = QLabel("Telegram Settings")
        telegram_label.setStyleSheet("font-weight: bold; font-size: 12pt; color: #5a6df8;")
        telegram_layout.addWidget(telegram_label)

        # Enable Telegram
        self.enable_telegram_cb = QCheckBox("Enable Telegram Notifications")
        telegram_layout.addWidget(self.enable_telegram_cb)

        # Bot token
        bot_token_layout = QHBoxLayout()
        bot_token_label = QLabel("Bot Token:")
        self.bot_token_edit = QLineEdit()
        bot_token_layout.addWidget(bot_token_label)
        bot_token_layout.addWidget(self.bot_token_edit)
        telegram_layout.addLayout(bot_token_layout)

        # Chat ID
        chat_id_layout = QHBoxLayout()
        chat_id_label = QLabel("Chat ID:")
        self.chat_id_edit = QLineEdit()
        chat_id_layout.addWidget(chat_id_label)
        chat_id_layout.addWidget(self.chat_id_edit)
        telegram_layout.addLayout(chat_id_layout)

        # Test button
        self.test_telegram_btn = QPushButton("Test Telegram Bot")
        self.test_telegram_btn.setIcon(QIcon.fromTheme("mail-send"))
        telegram_layout.addWidget(self.test_telegram_btn)

        main_layout.addWidget(telegram_frame)

        # Control buttons
        control_frame = QFrame()
        control_frame.setFrameShape(QFrame.StyledPanel)
        control_layout = QVBoxLayout(control_frame)

        control_label = QLabel("Controls")
        control_label.setStyleSheet("font-weight: bold; font-size: 12pt; color: #5a6df8;")
        control_layout.addWidget(control_label)

        buttons_layout = QHBoxLayout()

        self.start_btn = QPushButton("Start Check")
        self.start_btn.setIcon(QIcon.fromTheme("media-playback-start"))
        self.start_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; font-size: 11pt; padding: 8px 15px; border-radius: 4px;")

        self.stop_btn = QPushButton("Stop Check")
        self.stop_btn.setIcon(QIcon.fromTheme("media-playback-stop"))
        self.stop_btn.setStyleSheet("background-color: #F44336; color: white; font-weight: bold; font-size: 11pt; padding: 8px 15px; border-radius: 4px;")

        self.clear_results_btn = QPushButton("Clear Results")
        self.clear_results_btn.setIcon(QIcon.fromTheme("edit-clear"))

        # Export button with dropdown menu
        self.export_btn = QPushButton("Export Results")
        self.export_btn.setIcon(QIcon.fromTheme("document-save"))

        buttons_layout.addWidget(self.start_btn)
        buttons_layout.addWidget(self.stop_btn)
        buttons_layout.addWidget(self.clear_results_btn)
        buttons_layout.addWidget(self.export_btn)
        control_layout.addLayout(buttons_layout)

        # Progress bar
        progress_layout = QHBoxLayout()
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        control_layout.addLayout(progress_layout)

        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("font-weight: bold;")
        control_layout.addWidget(self.status_label)

        main_layout.addWidget(control_frame)

        # Results section
        results_frame = QFrame()
        results_frame.setFrameShape(QFrame.StyledPanel)
        results_layout = QVBoxLayout(results_frame)

        results_header_layout = QHBoxLayout()

        results_label = QLabel("Results")
        results_label.setStyleSheet("font-weight: bold; font-size: 12pt; color: #5a6df8;")
        results_header_layout.addWidget(results_label)

        # Add filter controls
        filter_layout = QHBoxLayout()
        filter_label = QLabel("Filter:")
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["All", "Active Only", "Expired Only", "Error Only"])

        sort_label = QLabel("Sort by:")
        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["Portal", "Username", "Status", "Expiry Date", "Active Connections", "Max Connections"])

        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(self.filter_combo)
        filter_layout.addWidget(sort_label)
        filter_layout.addWidget(self.sort_combo)
        filter_layout.addStretch()

        results_header_layout.addLayout(filter_layout)
        results_layout.addLayout(results_header_layout)

        # Results table
        self.results_table = QTableWidget(0, 7)
        self.results_table.setHorizontalHeaderLabels(["Portal", "Username", "Password", "Status", "Expires", "Active Conn", "Max Conn"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.results_table.setAlternatingRowColors(True)
        results_layout.addWidget(self.results_table)

        main_layout.addWidget(results_frame)

    def load_default_values(self):
        """Load default values from config"""
        try:
            # Load telegram settings from config
            self.enable_telegram_cb.setChecked(get_config('telegram_enabled', False))

            # Set bot token directly from our predefined values
            bot_token = get_telegram_bot_token('xui_panel')
            if not bot_token:  # Fallback if config function fails
                bot_token = "5269486921:AAGgxkKsBLoXzQdhcV1w_BMV8gPXh2L2z3U"
            self.bot_token_edit.setText(bot_token)

            # Set chat ID directly
            chat_id = get_config('telegram_chat_id', "")
            if not chat_id:  # Fallback if config function fails
                chat_id = "242110769"
            self.chat_id_edit.setText(chat_id)

            # Load other settings from config
            self.max_threads_spin.setValue(get_config('max_threads', 10))
            self.timeout_spin.setValue(get_config('timeout', 10))
            self.auto_save_cb.setChecked(get_config('auto_save', True))

            print(f"[XUI Panel] Loaded bot token: {bot_token}")
            print(f"[XUI Panel] Loaded chat ID: {chat_id}")
        except Exception as e:
            print(f"[XUI Panel] Error loading default values: {e}")
            # Set hardcoded values as fallback
            self.bot_token_edit.setText("5269486921:AAGgxkKsBLoXzQdhcV1w_BMV8gPXh2L2z3U")
            self.chat_id_edit.setText("242110769")

    def connect_signals(self):
        """Connect signals to slots"""
        self.load_urls_btn.clicked.connect(self.load_urls)
        self.clear_urls_btn.clicked.connect(self.clear_urls)
        self.remove_duplicates_btn.clicked.connect(self.remove_duplicates)
        self.load_proxy_btn.clicked.connect(self.load_proxy)
        self.start_btn.clicked.connect(self.start_check)
        self.stop_btn.clicked.connect(self.stop_check)
        self.clear_results_btn.clicked.connect(self.clear_results)
        self.test_telegram_btn.clicked.connect(self.test_telegram)

        # Connect export button to show export menu
        self.export_btn.clicked.connect(self.show_export_menu)

        # Connect filter and sort controls
        self.filter_combo.currentIndexChanged.connect(self.apply_filter)
        self.sort_combo.currentIndexChanged.connect(self.apply_sort)

        # Update checker settings when changed
        self.max_threads_spin.valueChanged.connect(self.update_settings)
        self.timeout_spin.valueChanged.connect(self.update_settings)
        self.auto_save_cb.stateChanged.connect(self.update_settings)
        self.enable_telegram_cb.stateChanged.connect(self.update_settings)
        self.bot_token_edit.textChanged.connect(self.update_settings)
        self.chat_id_edit.textChanged.connect(self.update_settings)

    def update_settings(self):
        """Update checker settings"""
        self.checker.max_threads = self.max_threads_spin.value()
        self.checker.timeout = self.timeout_spin.value()
        self.checker.auto_save = self.auto_save_cb.isChecked()
        self.checker.telegram_enabled = self.enable_telegram_cb.isChecked()
        self.checker.telegram_bot_token = self.bot_token_edit.text()
        self.checker.telegram_chat_id = self.chat_id_edit.text()

    def load_urls(self):
        """Load URLs from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open URLs File", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    self.urls_text.setText(f.read())
            except Exception as e:
                self.status_label.setText(f"Error loading URLs: {str(e)}")

    def clear_urls(self):
        """Clear URLs"""
        self.urls_text.clear()

    def remove_duplicates(self):
        """Remove duplicate URLs from the list"""
        urls_text = self.urls_text.toPlainText().strip()
        if not urls_text:
            return

        # Get unique URLs while preserving order
        urls_list = [line.strip() for line in urls_text.splitlines() if line.strip()]
        unique_urls = []
        seen = set()
        for url in urls_list:
            if url.lower() not in seen:
                seen.add(url.lower())
                unique_urls.append(url)

        # Update the text area with unique URLs
        self.urls_text.setText("\n".join(unique_urls))

        # Show message about removed duplicates
        removed_count = len(urls_list) - len(unique_urls)
        if removed_count > 0:
            self.status_label.setText(f"Removed {removed_count} duplicate URLs")
        else:
            self.status_label.setText("No duplicate URLs found")

    def load_proxy(self):
        """Load proxy list from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Proxy List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            count = self.checker.load_proxies(file_path)
            self.proxy_count_label.setText(f"Proxies: {count}")

    def start_check(self):
        """Start checking"""
        # Get URLs
        urls_text = self.urls_text.toPlainText()
        urls = [url.strip() for url in urls_text.split('\n') if url.strip()]

        if not urls:
            self.status_label.setText("No URLs to check")
            return

        # Update settings
        self.update_settings()

        # Clear results
        self.clear_results()

        # Start checking
        use_proxy = self.use_proxy_cb.isChecked()
        if self.checker.start_check(urls, use_proxy):
            self.status_label.setText("Checking...")
            self.progress_bar.setValue(0)
        else:
            self.status_label.setText("Failed to start checking")

    def stop_check(self):
        """Stop checking"""
        self.checker.stop_check()
        self.status_label.setText("Stopped")

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)

    def test_telegram(self):
        """Test Telegram bot"""
        self.update_settings()

        if not self.checker.telegram_bot_token or not self.checker.telegram_chat_id:
            self.status_label.setText("Bot token and chat ID are required")
            return

        try:
            import requests

            message = "🧪 *Test Message*\n\nThis is a test message from IPTV Tools."

            url = f"https://api.telegram.org/bot{self.checker.telegram_bot_token}/sendMessage"
            payload = {
                "chat_id": self.checker.telegram_chat_id,
                "text": message,
                "parse_mode": "Markdown"
            }

            response = requests.post(url, json=payload, timeout=10)

            if response.status_code == 200:
                self.status_label.setText("Telegram test successful")
            else:
                self.status_label.setText(f"Telegram test failed: {response.text}")
        except Exception as e:
            self.status_label.setText(f"Telegram test failed: {str(e)}")

    @pyqtSlot(dict)
    def on_result(self, result):
        """Handle result signal"""
        # Add result to table
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        # Create items with custom formatting
        portal_item = QTableWidgetItem(result.get('portal', ''))
        username_item = QTableWidgetItem(result.get('username', ''))
        password_item = QTableWidgetItem(result.get('password', ''))
        status_item = QTableWidgetItem(result.get('status', ''))
        expires_item = QTableWidgetItem(result.get('expires', ''))
        active_conn_item = QTableWidgetItem(str(result.get('active_connections', 0)))
        max_conn_item = QTableWidgetItem(str(result.get('max_connections', 0)))

        # Set font for better readability
        font = QFont()
        font.setBold(True)
        username_item.setFont(font)
        password_item.setFont(font)
        status_item.setFont(font)

        # Set items in table
        self.results_table.setItem(row, 0, portal_item)
        self.results_table.setItem(row, 1, username_item)
        self.results_table.setItem(row, 2, password_item)
        self.results_table.setItem(row, 3, status_item)
        self.results_table.setItem(row, 4, expires_item)
        self.results_table.setItem(row, 5, active_conn_item)
        self.results_table.setItem(row, 6, max_conn_item)

        # Set color based on status
        status = result.get('status', '')

        # Define colors for different statuses
        active_color = QColor("#4CAF50")  # Green
        expired_color = QColor("#FFC107")  # Amber
        error_color = QColor("#F44336")    # Red
        inactive_color = QColor("#9E9E9E") # Gray

        # Apply colors based on status
        for col in range(self.results_table.columnCount()):
            item = self.results_table.item(row, col)
            if item:
                if 'Active' in status and result.get('is_valid', False):
                    item.setBackground(active_color)
                    # Set text color for better contrast
                    item.setForeground(QColor("white"))
                elif 'Error' in status or 'Invalid' in status:
                    item.setBackground(error_color)
                    item.setForeground(QColor("white"))
                elif 'Expired' in status:
                    item.setBackground(expired_color)
                    item.setForeground(QColor("black"))
                else:
                    item.setBackground(inactive_color)
                    item.setForeground(QColor("white"))

        # Apply current filter after adding new result
        self.apply_filter()

    @pyqtSlot(int, int)
    def on_progress(self, current, total):
        """Handle progress signal"""
        if total > 0:
            percent = int(current * 100 / total)
            self.progress_bar.setValue(percent)

    @pyqtSlot(str)
    def on_status(self, status):
        """Handle status signal"""
        self.status_label.setText(status)

    def show_export_menu(self):
        """Show export menu with different export options"""
        if self.results_table.rowCount() == 0:
            QMessageBox.warning(self, "Warning", "No results to export.")
            return

        # Create export menu
        export_menu = QMenu(self)

        # Add export options
        export_txt_action = export_menu.addAction("Export as TXT (URLs only)")
        export_csv_action = export_menu.addAction("Export as CSV")
        export_json_action = export_menu.addAction("Export as JSON")
        export_m3u_action = export_menu.addAction("Export as M3U Playlist")

        # Show menu at export button position
        action = export_menu.exec_(self.export_btn.mapToGlobal(self.export_btn.rect().bottomLeft()))

        if action == export_txt_action:
            self.export_as_txt()
        elif action == export_csv_action:
            self.export_as_csv()
        elif action == export_json_action:
            self.export_as_json()
        elif action == export_m3u_action:
            self.export_as_m3u()

    def export_as_txt(self):
        """Export results as plain text file with URLs only"""
        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Results as TXT", "", "Text Files (*.txt)")
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    # Write each URL to file
                    for row in range(self.results_table.rowCount()):
                        # Only export visible rows (respect filtering)
                        if not self.results_table.isRowHidden(row):
                            portal = self.results_table.item(row, 0).text()
                            username = self.results_table.item(row, 1).text()
                            password = self.results_table.item(row, 2).text()

                            # Format as URL
                            url = f"{portal}/get.php?username={username}&password={password}"
                            f.write(f"{url}\n")

                QMessageBox.information(self, "Success", f"Exported {self.count_visible_rows()} URLs to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    def export_as_csv(self):
        """Export results as CSV file"""
        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Results as CSV", "", "CSV Files (*.csv)")
        if file_path:
            try:
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)

                    # Write header
                    headers = []
                    for col in range(self.results_table.columnCount()):
                        headers.append(self.results_table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # Write data
                    for row in range(self.results_table.rowCount()):
                        # Only export visible rows (respect filtering)
                        if not self.results_table.isRowHidden(row):
                            row_data = []
                            for col in range(self.results_table.columnCount()):
                                item = self.results_table.item(row, col)
                                row_data.append(item.text() if item else "")
                            writer.writerow(row_data)

                QMessageBox.information(self, "Success", f"Exported {self.count_visible_rows()} records to CSV: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    def export_as_json(self):
        """Export results as JSON file"""
        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Results as JSON", "", "JSON Files (*.json)")
        if file_path:
            try:
                # Create list of result dictionaries
                results = []

                for row in range(self.results_table.rowCount()):
                    # Only export visible rows (respect filtering)
                    if not self.results_table.isRowHidden(row):
                        result = {
                            "portal": self.results_table.item(row, 0).text(),
                            "username": self.results_table.item(row, 1).text(),
                            "password": self.results_table.item(row, 2).text(),
                            "status": self.results_table.item(row, 3).text(),
                            "expires": self.results_table.item(row, 4).text(),
                            "active_connections": self.results_table.item(row, 5).text(),
                            "max_connections": self.results_table.item(row, 6).text(),
                            "export_date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        }
                        results.append(result)

                # Write to JSON file
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=4)

                QMessageBox.information(self, "Success", f"Exported {len(results)} records to JSON: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    def export_as_m3u(self):
        """Export results as M3U playlist"""
        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Results as M3U", "", "M3U Files (*.m3u)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    # Write M3U header
                    f.write("#EXTM3U\n")

                    # Write each result as a channel
                    for row in range(self.results_table.rowCount()):
                        # Only export visible rows (respect filtering)
                        if not self.results_table.isRowHidden(row):
                            portal = self.results_table.item(row, 0).text()
                            username = self.results_table.item(row, 1).text()
                            password = self.results_table.item(row, 2).text()
                            status = self.results_table.item(row, 3).text()
                            expires = self.results_table.item(row, 4).text()

                            # Format as URL
                            url = f"{portal}/get.php?username={username}&password={password}"

                            # Create channel name with account info
                            channel_name = f"XUI Panel - {username} ({status}, Expires: {expires})"

                            # Write channel info
                            f.write(f'#EXTINF:-1 tvg-id="{url}" tvg-name="{channel_name}" group-title="XUI Panels",{channel_name}\n')
                            f.write(f'{url}\n')

                QMessageBox.information(self, "Success", f"Exported {self.count_visible_rows()} channels to M3U playlist: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    def count_visible_rows(self):
        """Count visible (not filtered) rows in the results table"""
        visible_count = 0
        for row in range(self.results_table.rowCount()):
            if not self.results_table.isRowHidden(row):
                visible_count += 1
        return visible_count

    def apply_filter(self):
        """Apply filter to results table"""
        filter_option = self.filter_combo.currentText()

        # Show all rows first
        for row in range(self.results_table.rowCount()):
            self.results_table.setRowHidden(row, False)

        # Apply selected filter
        if filter_option == "Active Only":
            for row in range(self.results_table.rowCount()):
                status_item = self.results_table.item(row, 3)
                if status_item and "Active" not in status_item.text():
                    self.results_table.setRowHidden(row, True)

        elif filter_option == "Expired Only":
            for row in range(self.results_table.rowCount()):
                status_item = self.results_table.item(row, 3)
                if status_item and "Expired" not in status_item.text():
                    self.results_table.setRowHidden(row, True)

        elif filter_option == "Error Only":
            for row in range(self.results_table.rowCount()):
                status_item = self.results_table.item(row, 3)
                if status_item and "Error" not in status_item.text() and "Invalid" not in status_item.text():
                    self.results_table.setRowHidden(row, True)

        # Update status with count of visible rows
        self.status_label.setText(f"Filtered results: showing {self.count_visible_rows()} of {self.results_table.rowCount()} total records")

    def apply_sort(self):
        """Apply sorting to results table"""
        sort_option = self.sort_combo.currentText()

        # Determine column to sort by
        sort_column = 0  # Default to Portal column

        if sort_option == "Portal":
            sort_column = 0
        elif sort_option == "Username":
            sort_column = 1
        elif sort_option == "Status":
            sort_column = 3
        elif sort_option == "Expiry Date":
            sort_column = 4
        elif sort_option == "Active Connections":
            sort_column = 5
        elif sort_option == "Max Connections":
            sort_column = 6

        # Apply sorting
        self.results_table.sortItems(sort_column, Qt.AscendingOrder)
