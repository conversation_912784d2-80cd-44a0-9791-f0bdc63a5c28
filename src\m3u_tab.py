import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QCheckBox, QSpinBox, QFileDialog, QProgressBar,
                            QGroupBox, QMessageBox)
from PyQt5.QtGui import QColor
from PyQt5.QtCore import Qt

from m3u_checker import <PERSON>3<PERSON><PERSON><PERSON>
from config import get_config, get_telegram_bot_token

class M3UTab(QWidget):
    """Tab for M3U Checker functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.checker = M3UChecker()
        self.init_ui()
        self.load_default_values()
        self.update_checker_settings()  # Update checker with loaded values
        self.connect_signals()

    def init_ui(self):
        layout = QVBoxLayout()

        # Configuration group
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout()

        # M3U URLs input
        urls_layout = QHBoxLayout()
        urls_layout.addWidget(QLabel("M3U URLs:"))
        self.urls_input = QTextEdit()
        self.urls_input.setPlaceholderText("Enter M3U URLs, one per line")
        self.urls_input.setMaximumHeight(100)
        urls_layout.addWidget(self.urls_input)
        config_layout.addLayout(urls_layout)

        # Buttons for loading URLs
        buttons_layout = QHBoxLayout()
        self.load_urls_button = QPushButton("Load URLs")
        self.load_urls_button.clicked.connect(self.load_urls)
        buttons_layout.addWidget(self.load_urls_button)

        self.load_file_button = QPushButton("Load External File")
        self.load_file_button.clicked.connect(self.load_external_file)
        buttons_layout.addWidget(self.load_file_button)

        self.clear_urls_button = QPushButton("Clear")
        self.clear_urls_button.clicked.connect(self.clear_urls)
        buttons_layout.addWidget(self.clear_urls_button)

        self.remove_duplicates_button = QPushButton("Remove Duplicates")
        self.remove_duplicates_button.clicked.connect(self.remove_duplicates)
        buttons_layout.addWidget(self.remove_duplicates_button)
        config_layout.addLayout(buttons_layout)

        # Proxy settings
        proxy_layout = QHBoxLayout()
        self.use_proxy = QCheckBox("Use Proxy")
        proxy_layout.addWidget(self.use_proxy)

        self.load_proxy_button = QPushButton("Load Proxy List")
        self.load_proxy_button.clicked.connect(self.load_proxy_list)
        proxy_layout.addWidget(self.load_proxy_button)

        self.proxy_count_label = QLabel("Proxies: 0")
        proxy_layout.addWidget(self.proxy_count_label)

        proxy_layout.addStretch()
        config_layout.addLayout(proxy_layout)

        # Thread settings
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Max Threads:"))
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(100)
        self.thread_count.setValue(10)
        thread_layout.addWidget(self.thread_count)

        thread_layout.addWidget(QLabel("Timeout (sec):"))
        self.timeout = QSpinBox()
        self.timeout.setMinimum(1)
        self.timeout.setMaximum(30)
        self.timeout.setValue(5)
        thread_layout.addWidget(self.timeout)

        thread_layout.addStretch()
        config_layout.addLayout(thread_layout)

        # Auto save option
        self.auto_save = QCheckBox("Auto-save good results to file")
        self.auto_save.setChecked(True)
        config_layout.addWidget(self.auto_save)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        # Telegram settings
        telegram_group = QGroupBox("Telegram Settings")
        telegram_layout = QVBoxLayout()

        # Enable Telegram
        self.telegram_enabled = QCheckBox("Enable Telegram Notifications")
        telegram_layout.addWidget(self.telegram_enabled)

        # Bot token
        token_layout = QHBoxLayout()
        token_layout.addWidget(QLabel("Bot Token:"))
        self.bot_token = QLineEdit()
        token_layout.addWidget(self.bot_token)
        telegram_layout.addLayout(token_layout)

        # Chat ID
        chat_id_layout = QHBoxLayout()
        chat_id_layout.addWidget(QLabel("Chat ID:"))
        self.chat_id = QLineEdit()
        chat_id_layout.addWidget(self.chat_id)
        telegram_layout.addLayout(chat_id_layout)

        # Test button
        self.test_button = QPushButton("Test Telegram Bot")
        self.test_button.clicked.connect(self.test_telegram)
        telegram_layout.addWidget(self.test_button)

        telegram_group.setLayout(telegram_layout)
        layout.addWidget(telegram_group)

        # Action buttons
        action_layout = QHBoxLayout()
        self.start_button = QPushButton("Start Check")
        self.start_button.clicked.connect(self.start_check)
        action_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("Stop Check")
        self.stop_button.clicked.connect(self.stop_check)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)

        self.clear_results_button = QPushButton("Clear Results")
        self.clear_results_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_results_button)

        layout.addLayout(action_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)

        # Results table
        self.results_table = QTableWidget(0, 5)
        self.results_table.setHorizontalHeaderLabels(["URL", "Status", "Channels", "Categories", "Expires"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.results_table)

        self.setLayout(layout)

    def load_default_values(self):
        """Load default values from config"""
        try:
            # Load telegram settings from config
            self.telegram_enabled.setChecked(get_config('telegram_enabled', False))

            # Set bot token directly from our predefined values
            bot_token = get_telegram_bot_token('m3u_iptv')
            if not bot_token:  # Fallback if config function fails
                bot_token = "6739447128:AAGRCcgny4XuTcg4qsuWbL8lXGvsLB8e-Xo"
            self.bot_token.setText(bot_token)

            # Set chat ID directly
            chat_id = get_config('telegram_chat_id', "")
            if not chat_id:  # Fallback if config function fails
                chat_id = "242110769"
            self.chat_id.setText(chat_id)

            # Load other settings from config
            self.thread_count.setValue(get_config('max_threads', 10))
            self.timeout.setValue(get_config('timeout', 5))
            self.auto_save.setChecked(get_config('auto_save', True))

            print(f"[M3U Checker] Loaded bot token: {bot_token}")
            print(f"[M3U Checker] Loaded chat ID: {chat_id}")
        except Exception as e:
            print(f"[M3U Checker] Error loading default values: {e}")
            # Set hardcoded values as fallback
            self.bot_token.setText("6739447128:AAGRCcgny4XuTcg4qsuWbL8lXGvsLB8e-Xo")
            self.chat_id.setText("242110769")

    def connect_signals(self):
        """Connect signals from checker to UI"""
        self.checker.result_signal.connect(self.add_result)
        self.checker.progress_signal.connect(self.update_progress)
        self.checker.status_signal.connect(self.update_status)

        # Update checker settings when UI changes
        self.thread_count.valueChanged.connect(self.update_checker_settings)
        self.timeout.valueChanged.connect(self.update_checker_settings)
        self.auto_save.toggled.connect(self.update_checker_settings)
        self.telegram_enabled.toggled.connect(self.update_checker_settings)
        self.bot_token.textChanged.connect(self.update_checker_settings)
        self.chat_id.textChanged.connect(self.update_checker_settings)

    def update_checker_settings(self):
        """Update checker settings from UI"""
        self.checker.max_threads = self.thread_count.value()
        self.checker.timeout = self.timeout.value()
        self.checker.auto_save = self.auto_save.isChecked()
        self.checker.telegram_enabled = self.telegram_enabled.isChecked()
        self.checker.telegram_bot_token = self.bot_token.text().strip()
        self.checker.telegram_chat_id = self.chat_id.text().strip()

    def load_urls(self):
        """Load M3U URLs from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open M3U URLs List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    urls = f.read()
                    self.urls_input.setText(urls)
                    self.status_label.setText(f"Loaded {len(urls.splitlines())} URLs from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load URLs: {str(e)}")

    def load_external_file(self):
        """Load and check external M3U file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open M3U File", "", "M3U Files (*.m3u *.m3u8);;All Files (*)")
        if file_path:
            try:
                # Read file content
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # Check if it's a valid M3U file
                if not content.strip().startswith('#EXTM3U') and '#EXTINF' not in content:
                    QMessageBox.warning(self, "Warning", "The selected file doesn't appear to be a valid M3U file.")
                    return

                # Add file path to URLs input
                current_text = self.urls_input.toPlainText()
                file_url = f"file://{file_path}"

                if current_text:
                    self.urls_input.setText(current_text + "\n" + file_url)
                else:
                    self.urls_input.setText(file_url)

                self.status_label.setText(f"Loaded external file: {file_path}")

                # Ask if user wants to start checking immediately
                reply = QMessageBox.question(self, "Start Check",
                                           "Do you want to start checking this file now?",
                                           QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)

                if reply == QMessageBox.Yes:
                    self.start_check()

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load external file: {str(e)}")

    def clear_urls(self):
        """Clear M3U URLs"""
        self.urls_input.clear()

    def remove_duplicates(self):
        """Remove duplicate URLs from the list"""
        urls_text = self.urls_input.toPlainText().strip()
        if not urls_text:
            return

        # Get unique URLs while preserving order
        urls_list = [line.strip() for line in urls_text.splitlines() if line.strip()]
        unique_urls = []
        seen = set()
        for url in urls_list:
            if url.lower() not in seen:
                seen.add(url.lower())
                unique_urls.append(url)

        # Update the text area with unique URLs
        self.urls_input.setText("\n".join(unique_urls))

        # Show message about removed duplicates
        removed_count = len(urls_list) - len(unique_urls)
        if removed_count > 0:
            self.status_label.setText(f"Removed {removed_count} duplicate URLs")
        else:
            self.status_label.setText("No duplicate URLs found")

    def load_proxy_list(self):
        """Load proxy list from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Proxy List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                count = self.checker.load_proxies(file_path)
                self.proxy_count_label.setText(f"Proxies: {count}")
                self.status_label.setText(f"Loaded {count} proxies from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load proxy list: {str(e)}")

    def test_telegram(self):
        """Test Telegram bot connection"""
        import requests

        if not self.telegram_enabled.isChecked():
            QMessageBox.warning(self, "Warning", "Telegram notifications are not enabled.")
            return

        token = self.bot_token.text().strip()
        chat_id = self.chat_id.text().strip()

        if not token or not chat_id:
            QMessageBox.warning(self, "Warning", "Please enter both Bot Token and Chat ID.")
            return

        try:
            message = "🧪 Test message from IPTV Tools by Manzera Ayenna"
            url = f"https://api.telegram.org/bot{token}/sendMessage"
            payload = {
                "chat_id": chat_id,
                "text": message
            }

            response = requests.post(url, json=payload, timeout=10)

            if response.status_code == 200:
                QMessageBox.information(self, "Success", "Test message sent successfully!")
            else:
                data = response.json()
                QMessageBox.critical(self, "Error", f"Failed to send message: {data.get('description', 'Unknown error')}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to send message: {str(e)}")

    def start_check(self):
        """Start M3U checking"""
        # Get M3U URLs list
        urls_text = self.urls_input.toPlainText().strip()
        if not urls_text:
            QMessageBox.warning(self, "Warning", "Please enter at least one M3U URL.")
            return

        urls_list = [line.strip() for line in urls_text.splitlines() if line.strip()]

        # Update checker settings
        self.update_checker_settings()

        # Start checking
        success = self.checker.start_check(urls_list, self.use_proxy.isChecked())

        if success:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("Checking...")
            self.progress_bar.setMaximum(len(urls_list))
            self.progress_bar.setValue(0)
        else:
            QMessageBox.critical(self, "Error", "Failed to start checking.")

    def stop_check(self):
        """Stop M3U checking"""
        self.checker.stop_check()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("Check stopped")

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)

    def add_result(self, result):
        """Add result to table"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(result.get('url', '')))
        self.results_table.setItem(row, 1, QTableWidgetItem(result.get('status', '')))
        self.results_table.setItem(row, 2, QTableWidgetItem(str(result.get('channels', ''))))

        # Format categories
        categories = result.get('categories', [])
        if categories:
            categories_str = ', '.join(categories[:5])
            if len(categories) > 5:
                categories_str += f" and {len(categories) - 5} more"
        else:
            categories_str = ""

        self.results_table.setItem(row, 3, QTableWidgetItem(categories_str))
        self.results_table.setItem(row, 4, QTableWidgetItem(result.get('expires', '')))

        # Highlight row
        for col in range(self.results_table.columnCount()):
            item = self.results_table.item(row, col)
            if item:
                item.setBackground(QColor(200, 255, 200))  # Light green

    def update_progress(self, current, total):
        """Update progress bar"""
        self.progress_bar.setValue(current)
        if current >= total:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText(f"Check completed. Found {self.results_table.rowCount()} valid M3U playlists.")

    def update_status(self, status):
        """Update status label"""
        self.status_label.setText(status)
