"""
Stream Analyzer module for IPTV CHECKER - TWINS.
This module provides functionality to analyze IPTV streams.
"""

import os
import re
import time
import logging
import threading
import subprocess
import tempfile
from datetime import datetime
from urllib.parse import urlparse

import requests
from PyQt5.QtCore import QObject, pyqtSignal

# Import FFmpeg helper
from ffmpeg_helper import check_ffmpeg

# Get logger
logger = logging.getLogger("iptv_checker.stream_analyzer")

class StreamAnalyzer(QObject):
    """Class for analyzing IPTV streams"""

    # Define signals
    result_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)
    status_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False
        self.timeout = 30  # Default timeout in seconds
        self.ffmpeg_path = self._find_ffmpeg()
        logger.info(f"FFmpeg path: {self.ffmpeg_path}")

    def _find_ffmpeg(self):
        """Find FFmpeg executable path"""
        # Use the FFmpeg helper to check for and download FFmpeg if needed
        return check_ffmpeg()

    def _is_ffmpeg_available(self):
        """Check if FFmpeg is available"""
        try:
            # Try to run FFmpeg with -version flag
            process = subprocess.run(
                [self.ffmpeg_path, '-version'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=5
            )
            return process.returncode == 0
        except Exception as e:
            logger.error(f"Error checking FFmpeg availability: {str(e)}")
            return False

    def analyze_stream(self, url, check_video=True, check_audio=True, check_subtitles=True, duration=10):
        """
        Analyze a stream and return its properties

        Args:
            url (str): Stream URL
            check_video (bool): Whether to check video streams
            check_audio (bool): Whether to check audio streams
            check_subtitles (bool): Whether to check subtitle streams
            duration (int): Duration in seconds to analyze

        Returns:
            dict: Stream analysis results
        """
        self.running = True
        self.status_signal.emit(f"Analyzing stream: {url}")
        self.progress_signal.emit(0, 100)  # Initialize progress

        result = {
            'url': url,
            'is_valid': False,
            'status': 'Checking...',
            'format': 'Unknown',
            'duration': 'Unknown',
            'video_streams': [],
            'audio_streams': [],
            'subtitle_streams': [],
            'bitrate': 'Unknown',
            'error': None,
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        try:
            # First check if URL is accessible
            self.status_signal.emit("Checking URL accessibility...")
            self.progress_signal.emit(10, 100)  # Update progress

            # Check if analysis was cancelled
            if not self.running:
                result['error'] = "Analysis cancelled by user"
                result['status'] = 'Cancelled'
                self.result_signal.emit(result)
                return result

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            # Parse URL to check protocol
            parsed_url = urlparse(url)

            # For HTTP/HTTPS URLs, check accessibility
            if parsed_url.scheme in ['http', 'https']:
                try:
                    response = requests.head(url, headers=headers, timeout=10, allow_redirects=True)
                    if response.status_code >= 400:
                        result['error'] = f"HTTP error: {response.status_code}"
                        result['status'] = 'Error'
                        self.status_signal.emit(f"HTTP error: {response.status_code}")
                        self.result_signal.emit(result)
                        return result
                except requests.exceptions.RequestException as e:
                    # Don't fail immediately, some streams don't support HEAD requests
                    logger.warning(f"HEAD request failed: {str(e)}, trying GET request")
                    try:
                        # Try a GET request with stream=True to avoid downloading the entire content
                        response = requests.get(url, headers=headers, timeout=10, stream=True)
                        response.close()  # Close the connection immediately
                        if response.status_code >= 400:
                            result['error'] = f"HTTP error: {response.status_code}"
                            result['status'] = 'Error'
                            self.status_signal.emit(f"HTTP error: {response.status_code}")
                            self.result_signal.emit(result)
                            return result
                    except requests.exceptions.RequestException as e:
                        result['error'] = f"Connection error: {str(e)}"
                        result['status'] = 'Error'
                        self.status_signal.emit(f"Connection error: {str(e)}")
                        self.result_signal.emit(result)
                        return result

            # Update progress
            self.progress_signal.emit(20, 100)

            # Use FFmpeg to analyze the stream
            self.status_signal.emit("Analyzing stream with FFmpeg...")
            self.progress_signal.emit(30, 100)  # Update progress

            # Check if analysis was cancelled
            if not self.running:
                result['error'] = "Analysis cancelled by user"
                result['status'] = 'Cancelled'
                self.result_signal.emit(result)
                return result

            # Check if FFmpeg is available
            if self.ffmpeg_path == 'ffmpeg' and not self._is_ffmpeg_available():
                result['error'] = "FFmpeg not found. Please install FFmpeg to analyze streams."
                result['status'] = 'Error'
                self.status_signal.emit("FFmpeg not found")
                self.result_signal.emit(result)
                return result

            # Create a temporary file for FFmpeg output
            with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
                temp_filename = temp_file.name

            try:
                # Build FFmpeg command
                ffmpeg_cmd = [
                    self.ffmpeg_path,
                    '-hide_banner',
                    '-loglevel', 'info',
                    '-i', url,
                    '-t', str(duration),
                    '-c', 'copy',
                    '-f', 'null',
                    '-'
                ]

                # Run FFmpeg process
                process = subprocess.Popen(
                    ffmpeg_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

                self.status_signal.emit("FFmpeg analyzing stream content...")
                self.progress_signal.emit(40, 100)  # Update progress

                # Monitor FFmpeg process
                start_time = time.time()
                ffmpeg_output = ""

                while process.poll() is None:
                    # Check if analysis was cancelled
                    if not self.running:
                        process.kill()
                        result['error'] = "Analysis cancelled by user"
                        result['status'] = 'Cancelled'
                        self.result_signal.emit(result)
                        return result

                    # Check if we've exceeded the timeout
                    elapsed = time.time() - start_time
                    if elapsed > duration + 10:
                        process.kill()
                        result['error'] = "FFmpeg process timed out"
                        result['status'] = 'Error'
                        self.status_signal.emit("FFmpeg process timed out")
                        self.result_signal.emit(result)
                        return result

                    # Update progress based on elapsed time
                    progress = min(90, 40 + int((elapsed / (duration + 5)) * 50))
                    self.progress_signal.emit(progress, 100)

                    # Sleep briefly to avoid high CPU usage
                    time.sleep(0.1)

                # Get FFmpeg output
                stdout, stderr = process.communicate()
                ffmpeg_output = stderr  # FFmpeg outputs info to stderr

                self.status_signal.emit("Parsing FFmpeg output...")
                self.progress_signal.emit(90, 100)  # Update progress

                # Parse FFmpeg output
                result.update(self._parse_ffmpeg_output(ffmpeg_output))

                # If we got stream info, mark as valid
                if result['format'] != 'Unknown' or result['video_streams'] or result['audio_streams']:
                    result['is_valid'] = True
                    result['status'] = 'Active'
                else:
                    result['status'] = 'Invalid'
                    result['error'] = "Could not detect valid stream format"

                self.progress_signal.emit(95, 100)  # Update progress

            except Exception as e:
                logger.error(f"Error analyzing stream with FFmpeg: {str(e)}")
                result['error'] = f"Analysis error: {str(e)}"
                result['status'] = 'Error'

            finally:
                # Clean up temp file
                if os.path.exists(temp_filename):
                    try:
                        os.unlink(temp_filename)
                    except:
                        pass

        except Exception as e:
            logger.error(f"Error analyzing stream: {str(e)}")
            result['error'] = f"Error: {str(e)}"
            result['status'] = 'Error'

        finally:
            self.running = False
            self.status_signal.emit("Analysis complete")
            self.progress_signal.emit(100, 100)  # Complete progress
            self.result_signal.emit(result)
            return result

    def _parse_ffmpeg_output(self, output):
        """Parse FFmpeg output to extract stream information"""
        result = {
            'format': 'Unknown',
            'duration': 'Unknown',
            'video_streams': [],
            'audio_streams': [],
            'subtitle_streams': [],
            'bitrate': 'Unknown'
        }

        # Extract format information
        format_match = re.search(r'Input #0, ([^,]+)', output)
        if format_match:
            result['format'] = format_match.group(1).strip()

        # Extract duration
        duration_match = re.search(r'Duration: ([^,]+)', output)
        if duration_match:
            result['duration'] = duration_match.group(1).strip()

        # Extract bitrate
        bitrate_match = re.search(r'bitrate: ([^,]+)', output)
        if bitrate_match:
            result['bitrate'] = bitrate_match.group(1).strip()

        # Extract stream information
        stream_matches = re.finditer(r'Stream #0:(\d+)(?:\(([^)]+)\))?: ([^:]+): ([^,]+)(?:, ([^,]+))?(?:, ([^,]+))?', output)
        for match in stream_matches:
            stream_index = match.group(1)
            stream_lang = match.group(2) if match.group(2) else 'und'
            stream_type = match.group(3).strip()
            stream_codec = match.group(4).strip()

            stream_info = {
                'index': stream_index,
                'language': stream_lang,
                'codec': stream_codec
            }

            # Add additional info if available
            if match.group(5):
                if 'x' in match.group(5):  # Likely resolution
                    stream_info['resolution'] = match.group(5).strip()
                elif 'Hz' in match.group(5):  # Likely audio sample rate
                    stream_info['sample_rate'] = match.group(5).strip()

            if match.group(6):
                stream_info['additional_info'] = match.group(6).strip()

            # Categorize stream by type
            if stream_type.lower() == 'video':
                result['video_streams'].append(stream_info)
            elif stream_type.lower() == 'audio':
                result['audio_streams'].append(stream_info)
            elif stream_type.lower() in ['subtitle', 'subtitles']:
                result['subtitle_streams'].append(stream_info)

        return result
