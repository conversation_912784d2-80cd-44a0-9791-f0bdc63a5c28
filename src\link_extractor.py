"""
Link Extractor module for IPTV Tools.
This module extracts Xtream Code and XUI Panel links from text files in a directory.
"""

import os
import re
import logging
from PyQt5.QtCore import QObject, pyqtSignal

# Get logger
logger = logging.getLogger("iptv_checker.link_extractor")

class LinkExtractor(QObject):
    """Class for extracting links from text files in a directory"""

    # Define signals
    progress_signal = pyqtSignal(int, int)
    status_signal = pyqtSignal(str)
    result_signal = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.xtream_links = []
        self.xui_links = []
        self.total_files = 0
        self.processed_files = 0
        # Maximum file size to process in one go (100MB)
        self.max_file_size = 100 * 1024 * 1024
        # Chunk size for reading large files (1MB)
        self.chunk_size = 1 * 1024 * 1024

    def extract_links_from_directory(self, directory_path):
        """
        Extract links from all text files in a directory.

        Args:
            directory_path (str): Path to directory containing text files

        Returns:
            tuple: (xtream_links, xui_links, total_files)
        """
        self.xtream_links = []
        self.xui_links = []
        self.total_files = 0
        self.processed_files = 0

        if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
            self.status_signal.emit(f"Directory not found: {directory_path}")
            return [], [], 0

        # Get all text files in directory
        text_files = [f for f in os.listdir(directory_path) if f.endswith('.txt')]
        self.total_files = len(text_files)

        if self.total_files == 0:
            self.status_signal.emit("No text files found in directory")
            return [], [], 0

        self.status_signal.emit(f"Found {self.total_files} text files. Starting extraction...")

        # Process each file
        for file_name in text_files:
            file_path = os.path.join(directory_path, file_name)
            self.processed_files += 1

            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                    # Extract Xtream Code links
                    xtream_links = self.extract_xtream_links(content)
                    self.xtream_links.extend(xtream_links)

                    # Extract XUI Panel links
                    xui_links = self.extract_xui_links(content)
                    self.xui_links.extend(xui_links)

                    # Emit result for this file
                    result = {
                        'file_name': file_name,
                        'xtream_count': len(xtream_links),
                        'xui_count': len(xui_links),
                        'total_links': len(xtream_links) + len(xui_links)
                    }
                    self.result_signal.emit(result)

            except Exception as e:
                logger.error(f"Error processing file {file_name}: {str(e)}")

            # Update progress
            self.progress_signal.emit(self.processed_files, self.total_files)
            self.status_signal.emit(f"Processed {self.processed_files}/{self.total_files} files. Found {len(self.xtream_links)} Xtream links and {len(self.xui_links)} XUI links.")

        # Final status update
        self.status_signal.emit(f"Extraction completed. Found {len(self.xtream_links)} Xtream links and {len(self.xui_links)} XUI links in {self.total_files} files.")

        return self.xtream_links, self.xui_links, self.total_files

    def extract_xtream_links(self, content):
        """
        Extract Xtream Code links from text content.

        Args:
            content (str): Text content to extract links from

        Returns:
            list: List of extracted Xtream Code links
        """
        links = []

        # Pattern 1: Standard Xtream Code URL format
        pattern1 = r'(https?://[^/\s]+)/(?:c/|get\.php\?username=)([^&\s]+)&password=([^&\s]+)'
        matches1 = re.findall(pattern1, content)
        for portal, username, password in matches1:
            # Format as standard Xtream Code URL
            link = f"{portal}/get.php?username={username}&password={password}"
            links.append(link)

        # Pattern 2: Alternative format with player_api.php
        pattern2 = r'(https?://[^/\s]+)/player_api\.php\?username=([^&\s]+)&password=([^&\s]+)'
        matches2 = re.findall(pattern2, content)
        for portal, username, password in matches2:
            # Format as standard Xtream Code URL
            link = f"{portal}/get.php?username={username}&password={password}"
            links.append(link)

        return links

    def extract_xui_links(self, content):
        """
        Extract XUI Panel links from text content.

        Args:
            content (str): Text content to extract links from

        Returns:
            list: List of extracted XUI Panel links
        """
        links = []

        # Pattern 1: Standard XUI Panel URL format
        pattern1 = r'(https?://[^/\s]+)/panel_api\.php\?username=([^&\s]+)&password=([^&\s]+)'
        matches1 = re.findall(pattern1, content)
        for portal, username, password in matches1:
            # Format as standard XUI Panel URL
            link = f"{portal}/panel_api.php?username={username}&password={password}"
            links.append(link)

        # Pattern 2: Look for URLs with /xmltv.php which are often XUI Panel
        pattern2 = r'(https?://[^/\s]+)/xmltv\.php\?username=([^&\s]+)&password=([^&\s]+)'
        matches2 = re.findall(pattern2, content)
        for portal, username, password in matches2:
            # Format as standard XUI Panel URL
            link = f"{portal}/panel_api.php?username={username}&password={password}"
            links.append(link)

        return links

    def extract_links_from_directory_method2(self, directory_path):
        """
        Extract links from all text files in a directory using the advanced method.
        Optimized for large files by processing in chunks.

        Args:
            directory_path (str): Path to directory containing text files

        Returns:
            tuple: (xtream_links, xui_links, total_files)
        """
        self.xtream_links = []
        self.xui_links = []
        self.total_files = 0
        self.processed_files = 0

        if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
            self.status_signal.emit(f"Directory not found: {directory_path}")
            return [], [], 0

        # Get all text files in directory
        text_files = [f for f in os.listdir(directory_path) if f.endswith('.txt')]
        self.total_files = len(text_files)

        if self.total_files == 0:
            self.status_signal.emit("No text files found in directory")
            return [], [], 0

        self.status_signal.emit(f"Found {self.total_files} text files. Starting advanced extraction...")

        # Process each file
        for file_name in text_files:
            file_path = os.path.join(directory_path, file_name)
            self.processed_files += 1
            file_xtream_links = []
            file_xui_links = []

            try:
                # Get file size
                file_size = os.path.getsize(file_path)

                # For small files, process normally
                if file_size <= self.max_file_size:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                        # Extract links with advanced methods
                        file_xtream_links = self.extract_xtream_links_advanced(content)
                        file_xui_links = self.extract_xui_links_advanced(content)

                # For large files, process in chunks
                else:
                    self.status_signal.emit(f"Processing large file: {file_name} ({file_size/1024/1024:.2f} MB)")

                    # Process file in chunks
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        # Buffer for overlapping content between chunks
                        overlap_buffer = ""
                        chunk_count = 0

                        while True:
                            chunk = f.read(self.chunk_size)
                            if not chunk:
                                break

                            chunk_count += 1

                            # Combine with overlap from previous chunk
                            combined_chunk = overlap_buffer + chunk

                            # Extract links from this chunk
                            chunk_xtream_links = self.extract_xtream_links_advanced(combined_chunk)
                            chunk_xui_links = self.extract_xui_links_advanced(combined_chunk)

                            # Add to file results
                            file_xtream_links.extend(chunk_xtream_links)
                            file_xui_links.extend(chunk_xui_links)

                            # Keep last 1000 characters for overlap
                            overlap_buffer = combined_chunk[-1000:] if len(combined_chunk) > 1000 else combined_chunk

                            # Update status periodically
                            if chunk_count % 10 == 0:
                                self.status_signal.emit(f"Processing {file_name}: Chunk {chunk_count}, found {len(file_xtream_links)} Xtream links and {len(file_xui_links)} XUI links so far")

                # Remove duplicates
                file_xtream_links = list(set(file_xtream_links))
                file_xui_links = list(set(file_xui_links))

                # Add to total results
                self.xtream_links.extend(file_xtream_links)
                self.xui_links.extend(file_xui_links)

                # Emit result for this file
                result = {
                    'file_name': file_name,
                    'xtream_count': len(file_xtream_links),
                    'xui_count': len(file_xui_links),
                    'total_links': len(file_xtream_links) + len(file_xui_links)
                }
                self.result_signal.emit(result)

            except Exception as e:
                logger.error(f"Error processing file {file_name}: {str(e)}")

            # Update progress
            self.progress_signal.emit(self.processed_files, self.total_files)
            self.status_signal.emit(f"Processed {self.processed_files}/{self.total_files} files. Found {len(self.xtream_links)} Xtream links and {len(self.xui_links)} XUI links.")

        # Remove duplicates from final results
        self.xtream_links = list(set(self.xtream_links))
        self.xui_links = list(set(self.xui_links))

        # Final status update
        self.status_signal.emit(f"Advanced extraction completed. Found {len(self.xtream_links)} Xtream links and {len(self.xui_links)} XUI links in {self.total_files} files.")

        return self.xtream_links, self.xui_links, self.total_files

    def extract_xtream_links_advanced(self, content):
        """
        Extract Xtream Code links from text content using advanced patterns.
        Improved version that focuses on legitimate IPTV patterns only.

        Args:
            content (str): Text content to extract links from

        Returns:
            list: List of extracted Xtream Code links
        """
        links = []

        # Pattern 1: Standard Xtream Code URL format
        pattern1 = r'(https?://[^/\s]+)/(?:c/|get\.php\?username=)([^&\s]+)&password=([^&\s]+)'
        matches1 = re.findall(pattern1, content)
        for portal, username, password in matches1:
            # Format as standard Xtream Code URL
            link = f"{portal}/get.php?username={username}&password={password}"
            links.append(link)

        # Pattern 2: Alternative format with player_api.php
        pattern2 = r'(https?://[^/\s]+)/player_api\.php\?username=([^&\s]+)&password=([^&\s]+)'
        matches2 = re.findall(pattern2, content)
        for portal, username, password in matches2:
            # Format as standard Xtream Code URL
            link = f"{portal}/get.php?username={username}&password={password}"
            links.append(link)

        # Pattern 3: Look for IPTV-specific portal patterns with credentials
        # Only look for URLs that are likely to be IPTV servers
        iptv_portal_pattern = r'(https?://(?:[^/\s]*(?:iptv|stream|tv|media|panel|server)[^/\s]*|(?:\d{1,3}\.){3}\d{1,3}(?::\d+)?)/[^/\s]*)'
        iptv_portals = re.findall(iptv_portal_pattern, content, re.IGNORECASE)

        # Look for usernames and passwords near IPTV portals
        for portal in iptv_portals[:5]:  # Limit to 5 portals to avoid spam
            # Look for username and password within 200 characters of the portal
            portal_index = content.find(portal)
            if portal_index != -1:
                # Extract context around the portal (±200 chars)
                start = max(0, portal_index - 200)
                end = min(len(content), portal_index + len(portal) + 200)
                context = content[start:end]

                # Look for credentials in the context
                username_matches = re.findall(r'(?:username|user|login)[^\w\n]*?[=:]\s*?([a-zA-Z0-9_]{3,20})', context, re.IGNORECASE)
                password_matches = re.findall(r'(?:password|pass|pwd)[^\w\n]*?[=:]\s*?([a-zA-Z0-9_]{3,20})', context, re.IGNORECASE)

                # Only create links if we find both username and password
                if username_matches and password_matches:
                    username = username_matches[0]
                    password = password_matches[0]

                    # Clean portal URL (remove any existing parameters)
                    clean_portal = portal.split('?')[0].rstrip('/')

                    # Format as standard Xtream Code URL
                    link = f"{clean_portal}/get.php?username={username}&password={password}"
                    links.append(link)

        # Pattern 4: Look for complete Xtream URLs that might be formatted differently
        pattern4 = r'(https?://[^/\s]+)(?:/[^?\s]*)?(?:\?|&)(?:.*?)?username=([^&\s]+)(?:.*?)?password=([^&\s]+)'
        matches4 = re.findall(pattern4, content)
        for portal, username, password in matches4:
            # Clean portal URL
            clean_portal = portal.rstrip('/')
            # Format as standard Xtream Code URL
            link = f"{clean_portal}/get.php?username={username}&password={password}"
            links.append(link)

        # Remove duplicates while preserving order
        seen = set()
        unique_links = []
        for link in links:
            if link not in seen:
                seen.add(link)
                unique_links.append(link)

        return unique_links

    def extract_xui_links_advanced(self, content):
        """
        Extract XUI Panel links from text content using advanced patterns.
        Improved version that focuses on legitimate IPTV XUI panels only.

        Args:
            content (str): Text content to extract links from

        Returns:
            list: List of extracted XUI Panel links
        """
        links = []

        # Pattern 1: Standard XUI Panel URL format
        pattern1 = r'(https?://[^/\s]+)/panel_api\.php\?username=([^&\s]+)&password=([^&\s]+)'
        matches1 = re.findall(pattern1, content)
        for portal, username, password in matches1:
            # Format as standard XUI Panel URL
            link = f"{portal}/panel_api.php?username={username}&password={password}"
            links.append(link)

        # Pattern 2: Look for URLs with /xmltv.php which are often XUI Panel
        pattern2 = r'(https?://[^/\s]+)/xmltv\.php\?username=([^&\s]+)&password=([^&\s]+)'
        matches2 = re.findall(pattern2, content)
        for portal, username, password in matches2:
            # Format as standard XUI Panel URL
            link = f"{portal}/panel_api.php?username={username}&password={password}"
            links.append(link)

        # Pattern 3: Look for XUI Panel specific URL patterns with credentials
        xui_pattern = r'(https?://[^/\s]+)(?:/panel/|/xui/|/ui/).*?username=([^&\s]+).*?password=([^&\s]+)'
        matches3 = re.findall(xui_pattern, content)
        for portal, username, password in matches3:
            # Format as standard XUI Panel URL
            link = f"{portal}/panel_api.php?username={username}&password={password}"
            links.append(link)

        # Pattern 4: Look for XUI-specific portal patterns with credentials nearby
        # Only look for URLs that are likely to be XUI panels
        xui_portal_pattern = r'(https?://(?:[^/\s]*(?:panel|xui|ui|iptv|stream|tv)[^/\s]*|(?:\d{1,3}\.){3}\d{1,3}(?::\d+)?)/(?:panel|xui|ui)[^/\s]*)'
        xui_portals = re.findall(xui_portal_pattern, content, re.IGNORECASE)

        # Look for usernames and passwords near XUI portals
        for portal in xui_portals[:5]:  # Limit to 5 portals to avoid spam
            # Look for username and password within 200 characters of the portal
            portal_index = content.find(portal)
            if portal_index != -1:
                # Extract context around the portal (±200 chars)
                start = max(0, portal_index - 200)
                end = min(len(content), portal_index + len(portal) + 200)
                context = content[start:end]

                # Look for credentials in the context
                username_matches = re.findall(r'(?:username|user|login)[^\w\n]*?[=:]\s*?([a-zA-Z0-9_]{3,20})', context, re.IGNORECASE)
                password_matches = re.findall(r'(?:password|pass|pwd)[^\w\n]*?[=:]\s*?([a-zA-Z0-9_]{3,20})', context, re.IGNORECASE)

                # Only create links if we find both username and password
                if username_matches and password_matches:
                    username = username_matches[0]
                    password = password_matches[0]

                    # Clean portal URL (remove any existing parameters)
                    clean_portal = portal.split('?')[0].rstrip('/')

                    # Format as standard XUI Panel URL
                    link = f"{clean_portal}/panel_api.php?username={username}&password={password}"
                    links.append(link)

        # Pattern 5: Look for complete XUI URLs that might be formatted differently
        pattern5 = r'(https?://[^/\s]+)(?:/[^?\s]*)?(?:\?|&)(?:.*?)?username=([^&\s]+)(?:.*?)?password=([^&\s]+)'
        matches5 = re.findall(pattern5, content)
        for portal, username, password in matches5:
            # Only process if it looks like a XUI panel
            if any(keyword in portal.lower() for keyword in ['panel', 'xui', 'ui']):
                # Clean portal URL
                clean_portal = portal.rstrip('/')
                # Format as standard XUI Panel URL
                link = f"{clean_portal}/panel_api.php?username={username}&password={password}"
                links.append(link)

        # Remove duplicates while preserving order
        seen = set()
        unique_links = []
        for link in links:
            if link not in seen:
                seen.add(link)
                unique_links.append(link)

        return unique_links

    def save_links_to_files(self, xtream_file='extracted_xtream_links.txt', xui_file='extracted_xui_links.txt'):
        """
        Save extracted links to files.

        Args:
            xtream_file (str): File to save Xtream Code links
            xui_file (str): File to save XUI Panel links

        Returns:
            tuple: (xtream_count, xui_count)
        """
        try:
            # Save Xtream Code links
            if self.xtream_links:
                with open(xtream_file, 'w', encoding='utf-8') as f:
                    for link in self.xtream_links:
                        f.write(f"{link}\n")
                logger.info(f"Saved {len(self.xtream_links)} Xtream Code links to {xtream_file}")

            # Save XUI Panel links
            if self.xui_links:
                with open(xui_file, 'w', encoding='utf-8') as f:
                    for link in self.xui_links:
                        f.write(f"{link}\n")
                logger.info(f"Saved {len(self.xui_links)} XUI Panel links to {xui_file}")

            return len(self.xtream_links), len(self.xui_links)

        except Exception as e:
            logger.error(f"Error saving links to files: {str(e)}")
            return 0, 0